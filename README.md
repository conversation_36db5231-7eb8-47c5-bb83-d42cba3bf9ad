# Rust 学习指南

这是一个全面的 Rust 编程语言学习项目，包含了 Rust 的核心概念和语法特性。每个文件都专注于一个特定的主题，并提供详细的示例和解释。

## 📚 学习内容

### 基础概念
1. **[01_variables_and_mutability.rs](src/01_variables_and_mutability.rs)** - 变量和可变性
   - 变量声明
   - 可变性 (mut)
   - 常量 (const)
   - 变量遮蔽 (shadowing)

2. **[02_data_types.rs](src/02_data_types.rs)** - 数据类型
   - 标量类型：整数、浮点、布尔、字符
   - 复合类型：元组、数组
   - 类型推断和注解
   - 类型转换

3. **[03_functions.rs](src/03_functions.rs)** - 函数
   - 函数定义和调用
   - 参数和返回值
   - 表达式 vs 语句
   - 闭包和高阶函数

4. **[04_control_flow.rs](src/04_control_flow.rs)** - 流程控制
   - if 表达式
   - 循环：loop、while、for
   - match 表达式
   - if let 和 while let

### 核心概念
5. **[05_ownership.rs](src/05_ownership.rs)** - 所有权
   - 所有权规则
   - 移动语义
   - 克隆 (clone)
   - 栈与堆

6. **[06_references_and_borrowing.rs](src/06_references_and_borrowing.rs)** - 引用和借用
   - 不可变引用
   - 可变引用
   - 借用规则
   - 切片 (slice)

7. **[07_structs.rs](src/07_structs.rs)** - 结构体
   - 结构体定义和实例化
   - 方法和关联函数
   - 元组结构体
   - 单元结构体

8. **[08_enums_and_pattern_matching.rs](src/08_enums_and_pattern_matching.rs)** - 枚举和模式匹配
   - 枚举定义
   - Option 枚举
   - match 表达式
   - if let 语法

### 集合和模块
9. **[09_collections.rs](src/09_collections.rs)** - 常见集合
   - Vec<T> 动态数组
   - String 字符串
   - HashMap<K, V> 哈希映射
   - 其他集合类型

10. **[10_modules_and_packages.rs](src/10_modules_and_packages.rs)** - 模块和包
    - 模块系统
    - use 关键字
    - 公有性和私有性
    - 包和 crate

### 错误处理和高级特性
11. **[11_error_handling.rs](src/11_error_handling.rs)** - 错误处理
    - panic! 和不可恢复错误
    - Result<T, E> 和可恢复错误
    - ? 运算符
    - 自定义错误类型

12. **[12_generics.rs](src/12_generics.rs)** - 泛型
    - 泛型函数
    - 泛型结构体和枚举
    - 泛型方法
    - 泛型约束

13. **[13_traits.rs](src/13_traits.rs)** - Trait
    - Trait 定义和实现
    - 默认实现
    - Trait 作为参数和返回值
    - 高级 Trait 特性

14. **[14_lifetimes.rs](src/14_lifetimes.rs)** - 生命周期
    - 生命周期基础
    - 函数和结构体中的生命周期
    - 生命周期省略规则
    - 静态生命周期

## 🚀 如何使用

### 运行单个学习文件
```bash
# 运行变量和可变性示例
cargo run --bin 01_variables_and_mutability

# 运行数据类型示例
cargo run --bin 02_data_types

# 运行函数示例
cargo run --bin 03_functions

# ... 以此类推
```

### 运行所有示例
```bash
# 按顺序运行所有示例
for i in {01..14}; do
    echo "=== 运行示例 $i ==="
    cargo run --bin $(ls src/${i}_*.rs | head -1 | sed 's/src\///g' | sed 's/\.rs//g')
    echo ""
done
```

### 查看可用的二进制目标
```bash
cargo build --bins
```

## 📖 学习建议

1. **按顺序学习**：建议按照编号顺序学习，因为后面的概念会建立在前面的基础上。

2. **动手实践**：不要只是阅读代码，尝试修改示例，看看会发生什么。

3. **理解概念**：每个文件都包含详细的注释和说明，确保理解每个概念的原理。

4. **实验和探索**：尝试创建自己的示例，应用学到的概念。

5. **参考文档**：结合 [Rust 官方文档](https://doc.rust-lang.org/book/) 学习。

## 🔧 环境要求

- Rust 1.70+ (推荐使用最新稳定版)
- Cargo (Rust 的包管理器)

### 安装 Rust
```bash
# 通过 rustup 安装 Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# 更新到最新版本
rustup update
```

## 📝 代码特点

- **详细注释**：每个概念都有中文注释说明
- **实用示例**：提供实际可运行的代码示例
- **最佳实践**：展示 Rust 的惯用写法
- **错误演示**：通过注释展示常见错误
- **性能说明**：解释 Rust 的零成本抽象

## 🎯 学习目标

通过这个项目，你将学会：

- Rust 的基本语法和概念
- 内存安全的编程方式
- 所有权系统的工作原理
- 如何处理错误
- 如何编写泛型和使用 trait
- 如何管理生命周期
- Rust 的模块系统
- 常用的数据结构和集合

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个学习资源！

## 📄 许可证

本项目采用 MIT 许可证。

---

**开始你的 Rust 学习之旅吧！🦀**
