[package]
name = "demo"
version = "0.1.0"
edition = "2024"

# 二进制目标 - Rust 学习系列
[[bin]]
name = "01_variables_and_mutability"
path = "src/01_variables_and_mutability.rs"

[[bin]]
name = "02_data_types"
path = "src/02_data_types.rs"

[[bin]]
name = "03_functions"
path = "src/03_functions.rs"

[[bin]]
name = "04_control_flow"
path = "src/04_control_flow.rs"

[[bin]]
name = "05_ownership"
path = "src/05_ownership.rs"

[[bin]]
name = "06_references_and_borrowing"
path = "src/06_references_and_borrowing.rs"

[[bin]]
name = "07_structs"
path = "src/07_structs.rs"

[[bin]]
name = "08_enums_and_pattern_matching"
path = "src/08_enums_and_pattern_matching.rs"

[[bin]]
name = "09_collections"
path = "src/09_collections.rs"

[[bin]]
name = "10_modules_and_packages"
path = "src/10_modules_and_packages.rs"

[[bin]]
name = "11_error_handling"
path = "src/11_error_handling.rs"

[[bin]]
name = "12_generics"
path = "src/12_generics.rs"

[[bin]]
name = "13_traits"
path = "src/13_traits.rs"

[[bin]]
name = "14_lifetimes"
path = "src/14_lifetimes.rs"

[dependencies]
