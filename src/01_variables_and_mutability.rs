// 01_variables_and_mutability.rs
// Rust 变量和可变性详解

fn main() {
    println!("=== Rust 变量和可变性 ===\n");

    // 1. 变量声明
    variables_declaration();
    
    // 2. 可变性
    mutability();
    
    // 3. 常量
    constants();
    
    // 4. 变量遮蔽 (Shadowing)
    shadowing();
}

// 1. 变量声明
fn variables_declaration() {
    println!("1. 变量声明:");
    
    // 默认情况下，变量是不可变的 (immutable)
    let x = 5;
    println!("  不可变变量 x = {}", x);
    
    // 尝试修改不可变变量会导致编译错误
    // x = 6; // 这行代码会导致编译错误
    
    // 使用 mut 关键字声明可变变量
    let mut y = 10;
    println!("  可变变量 y = {}", y);
    y = 15;
    println!("  修改后的 y = {}", y);
    
    println!();
}

// 2. 可变性详解
fn mutability() {
    println!("2. 可变性详解:");
    
    // 不可变变量
    let immutable_var = "Hello";
    println!("  不可变变量: {}", immutable_var);
    
    // 可变变量
    let mut mutable_var = String::from("World");
    println!("  可变变量 (修改前): {}", mutable_var);
    mutable_var.push_str("!");
    println!("  可变变量 (修改后): {}", mutable_var);
    
    // 可变引用
    let mut number = 42;
    let mutable_ref = &mut number;
    *mutable_ref += 8;
    println!("  通过可变引用修改: {}", number);
    
    println!();
}

// 3. 常量
fn constants() {
    println!("3. 常量:");
    
    // 常量必须使用 const 关键字，并且必须注明类型
    const MAX_POINTS: u32 = 100_000;
    const PI: f64 = 3.14159;
    
    println!("  常量 MAX_POINTS: {}", MAX_POINTS);
    println!("  常量 PI: {}", PI);
    
    // 常量可以在任何作用域中声明，包括全局作用域
    // 常量只能被设置为常量表达式，而不能是函数调用的结果
    
    println!();
}

// 4. 变量遮蔽 (Shadowing)
fn shadowing() {
    println!("4. 变量遮蔽 (Shadowing):");
    
    let x = 5;
    println!("  第一个 x: {}", x);
    
    // 使用相同的名字重新声明变量，这叫做遮蔽
    let x = x + 1;
    println!("  遮蔽后的 x: {}", x);
    
    {
        // 在内部作用域中遮蔽
        let x = x * 2;
        println!("  内部作用域的 x: {}", x);
    }
    
    println!("  外部作用域的 x: {}", x);
    
    // 遮蔽允许改变变量的类型
    let spaces = "   ";
    let spaces = spaces.len();
    println!("  类型改变: 从字符串到数字 {}", spaces);
    
    // 遮蔽 vs 可变性的区别
    demonstrate_shadowing_vs_mutability();
    
    println!();
}

fn demonstrate_shadowing_vs_mutability() {
    println!("  遮蔽 vs 可变性的区别:");
    
    // 遮蔽：创建新变量，可以改变类型
    let value = "123";
    let value = value.parse::<i32>().unwrap();
    println!("    遮蔽：字符串 -> 整数: {}", value);
    
    // 可变性：修改现有变量，不能改变类型
    let mut count = 0;
    count += 1;
    println!("    可变性：修改值: {}", count);
    
    // 以下代码会编译错误，因为不能改变可变变量的类型
    // count = "hello"; // 编译错误
}

// 全局常量示例
const GLOBAL_CONSTANT: &str = "这是一个全局常量";

// 静态变量 (很少使用，通常用常量代替)
static GLOBAL_STATIC: &str = "这是一个静态变量";

#[allow(dead_code)]
fn global_variables_example() {
    println!("全局常量: {}", GLOBAL_CONSTANT);
    println!("静态变量: {}", GLOBAL_STATIC);
}

/*
关键要点总结：

1. 变量默认是不可变的，这是 Rust 的安全特性之一
2. 使用 mut 关键字可以让变量变为可变的
3. 常量使用 const 关键字，必须注明类型，只能设置为常量表达式
4. 变量遮蔽允许重新声明同名变量，甚至可以改变类型
5. 遮蔽和可变性是不同的概念：
   - 遮蔽：创建新变量，可以改变类型
   - 可变性：修改现有变量，不能改变类型

编译和运行：
cargo run --bin 01_variables_and_mutability

或者在 main.rs 中调用：
mod 01_variables_and_mutability;
*/
