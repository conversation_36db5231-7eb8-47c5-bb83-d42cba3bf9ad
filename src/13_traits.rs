// 13_traits.rs
// Rust Trait 详解

fn main() {
    println!("=== Rust Trait ===\n");

    // 1. Trait 基础
    trait_basics();
    
    // 2. 默认实现
    default_implementations();
    
    // 3. Trait 作为参数
    traits_as_parameters();
    
    // 4. Trait 约束
    trait_bounds();
    
    // 5. 返回 Trait
    returning_traits();
    
    // 6. 高级 Trait 特性
    advanced_trait_features();
}

// 1. Trait 基础
fn trait_basics() {
    println!("1. Trait 基础:");
    
    // 定义 trait
    trait Summary {
        fn summarize(&self) -> String;
    }
    
    // 为结构体实现 trait
    struct NewsArticle {
        headline: String,
        location: String,
        author: String,
        content: String,
    }
    
    impl Summary for NewsArticle {
        fn summarize(&self) -> String {
            format!("{}, by {} ({})", self.headline, self.author, self.location)
        }
    }
    
    struct Tweet {
        username: String,
        content: String,
        reply: bool,
        retweet: bool,
    }
    
    impl Summary for Tweet {
        fn summarize(&self) -> String {
            format!("{}: {}", self.username, self.content)
        }
    }
    
    let article = NewsArticle {
        headline: String::from("Rust 1.0 发布"),
        location: String::from("美国"),
        author: String::from("Rust 团队"),
        content: String::from("Rust 编程语言正式发布 1.0 版本..."),
    };
    
    let tweet = Tweet {
        username: String::from("rust_lang"),
        content: String::from("Rust 是一门系统编程语言"),
        reply: false,
        retweet: false,
    };
    
    println!("  新闻摘要: {}", article.summarize());
    println!("  推特摘要: {}", tweet.summarize());
    
    println!();
}

// 2. 默认实现
fn default_implementations() {
    println!("2. 默认实现:");
    
    trait Summary {
        fn summarize_author(&self) -> String;
        
        // 默认实现
        fn summarize(&self) -> String {
            format!("(阅读更多来自 {} 的内容...)", self.summarize_author())
        }
    }
    
    struct Tweet {
        username: String,
        content: String,
        reply: bool,
        retweet: bool,
    }
    
    impl Summary for Tweet {
        fn summarize_author(&self) -> String {
            format!("@{}", self.username)
        }
        
        // 可以选择覆盖默认实现
        fn summarize(&self) -> String {
            format!("{}: {}", self.username, self.content)
        }
    }
    
    struct BlogPost {
        title: String,
        author: String,
        content: String,
    }
    
    impl Summary for BlogPost {
        fn summarize_author(&self) -> String {
            self.author.clone()
        }
        
        // 使用默认实现
    }
    
    let tweet = Tweet {
        username: String::from("horse_ebooks"),
        content: String::from("当然，正如你可能已经知道的，人们"),
        reply: false,
        retweet: false,
    };
    
    let blog = BlogPost {
        title: String::from("Rust 学习指南"),
        author: String::from("张三"),
        content: String::from("Rust 是一门现代系统编程语言..."),
    };
    
    println!("  推特摘要: {}", tweet.summarize());
    println!("  博客摘要: {}", blog.summarize());
    
    println!();
}

// 3. Trait 作为参数
fn traits_as_parameters() {
    println!("3. Trait 作为参数:");
    
    trait Summary {
        fn summarize(&self) -> String;
    }
    
    struct NewsArticle {
        headline: String,
        author: String,
    }
    
    impl Summary for NewsArticle {
        fn summarize(&self) -> String {
            format!("{} by {}", self.headline, self.author)
        }
    }
    
    struct Tweet {
        username: String,
        content: String,
    }
    
    impl Summary for Tweet {
        fn summarize(&self) -> String {
            format!("{}: {}", self.username, self.content)
        }
    }
    
    // impl Trait 语法
    fn notify(item: &impl Summary) {
        println!("  突发新闻！{}", item.summarize());
    }
    
    // Trait bound 语法
    fn notify_bound<T: Summary>(item: &T) {
        println!("  重要通知！{}", item.summarize());
    }
    
    // 多个 trait 约束
    fn notify_multiple(item: &(impl Summary + std::fmt::Display)) {
        println!("  多重约束：{}", item);
    }
    
    // 使用 where 子句
    fn notify_where<T>(item: &T)
    where
        T: Summary + std::fmt::Display,
    {
        println!("  Where 子句：{}", item);
    }
    
    let article = NewsArticle {
        headline: String::from("Rust 新特性"),
        author: String::from("开发者"),
    };
    
    let tweet = Tweet {
        username: String::from("rustacean"),
        content: String::from("学习 Rust trait"),
    };
    
    notify(&article);
    notify_bound(&tweet);
    
    // 为了演示多重约束，我们需要为类型实现 Display
    impl std::fmt::Display for NewsArticle {
        fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
            write!(f, "新闻：{}", self.headline)
        }
    }
    
    notify_multiple(&article);
    notify_where(&article);
    
    println!();
}

// 4. Trait 约束
fn trait_bounds() {
    println!("4. Trait 约束:");
    
    // 复杂的 trait 约束
    fn some_function<T, U>(_t: &T, _u: &U) -> i32
    where
        T: std::fmt::Display + Clone,
        U: Clone + std::fmt::Debug,
    {
        42
    }
    
    // 条件实现
    struct Pair<T> {
        x: T,
        y: T,
    }
    
    impl<T> Pair<T> {
        fn new(x: T, y: T) -> Self {
            Self { x, y }
        }
    }
    
    // 只为实现了特定 trait 的类型实现方法
    impl<T: std::fmt::Display + PartialOrd> Pair<T> {
        fn cmp_display(&self) {
            if self.x >= self.y {
                println!("  最大的成员是 x = {}", self.x);
            } else {
                println!("  最大的成员是 y = {}", self.y);
            }
        }
    }
    
    let pair = Pair::new(10, 20);
    pair.cmp_display();
    
    // blanket implementation（覆盖实现）
    trait MyTrait {
        fn my_method(&self);
    }
    
    // 为所有实现了 Display 的类型实现 MyTrait
    impl<T: std::fmt::Display> MyTrait for T {
        fn my_method(&self) {
            println!("  MyTrait 方法：{}", self);
        }
    }
    
    let number = 42;
    number.my_method(); // i32 实现了 Display，所以也有 MyTrait
    
    println!();
}

// 5. 返回 Trait
fn returning_traits() {
    println!("5. 返回 Trait:");
    
    trait Summary {
        fn summarize(&self) -> String;
    }
    
    struct NewsArticle {
        headline: String,
    }
    
    impl Summary for NewsArticle {
        fn summarize(&self) -> String {
            self.headline.clone()
        }
    }
    
    struct Tweet {
        content: String,
    }
    
    impl Summary for Tweet {
        fn summarize(&self) -> String {
            self.content.clone()
        }
    }
    
    // 返回实现了 trait 的类型
    fn returns_summarizable() -> impl Summary {
        NewsArticle {
            headline: String::from("返回的新闻"),
        }
    }
    
    let article = returns_summarizable();
    println!("  返回的摘要：{}", article.summarize());
    
    // 注意：不能根据条件返回不同的类型
    // fn returns_summarizable_conditional(switch: bool) -> impl Summary {
    //     if switch {
    //         NewsArticle { headline: String::from("新闻") }
    //     } else {
    //         Tweet { content: String::from("推特") } // 编译错误
    //     }
    // }
    
    // 使用 trait 对象可以返回不同类型
    fn returns_summarizable_dynamic(switch: bool) -> Box<dyn Summary> {
        if switch {
            Box::new(NewsArticle {
                headline: String::from("动态新闻"),
            })
        } else {
            Box::new(Tweet {
                content: String::from("动态推特"),
            })
        }
    }
    
    let dynamic_article = returns_summarizable_dynamic(true);
    let dynamic_tweet = returns_summarizable_dynamic(false);
    
    println!("  动态返回（新闻）：{}", dynamic_article.summarize());
    println!("  动态返回（推特）：{}", dynamic_tweet.summarize());
    
    println!();
}

// 6. 高级 Trait 特性
fn advanced_trait_features() {
    println!("6. 高级 Trait 特性:");
    
    // 关联类型
    associated_types();
    
    // 默认泛型类型参数和运算符重载
    operator_overloading();
    
    // 完全限定语法
    fully_qualified_syntax();
    
    // 超 trait
    supertraits();
    
    // newtype 模式
    newtype_pattern();
    
    println!();
}

fn associated_types() {
    println!("  关联类型:");
    
    trait Iterator {
        type Item;
        
        fn next(&mut self) -> Option<Self::Item>;
    }
    
    struct Counter {
        current: usize,
        max: usize,
    }
    
    impl Counter {
        fn new(max: usize) -> Counter {
            Counter { current: 0, max }
        }
    }
    
    impl Iterator for Counter {
        type Item = usize;
        
        fn next(&mut self) -> Option<Self::Item> {
            if self.current < self.max {
                let current = self.current;
                self.current += 1;
                Some(current)
            } else {
                None
            }
        }
    }
    
    let mut counter = Counter::new(3);
    while let Some(value) = counter.next() {
        println!("    计数器值: {}", value);
    }
}

fn operator_overloading() {
    println!("  运算符重载:");
    
    use std::ops::Add;
    
    #[derive(Debug, Copy, Clone, PartialEq)]
    struct Point {
        x: i32,
        y: i32,
    }
    
    impl Add for Point {
        type Output = Point;
        
        fn add(self, other: Point) -> Point {
            Point {
                x: self.x + other.x,
                y: self.y + other.y,
            }
        }
    }
    
    let p1 = Point { x: 1, y: 0 };
    let p2 = Point { x: 2, y: 3 };
    let p3 = p1 + p2;
    
    println!("    {:?} + {:?} = {:?}", p1, p2, p3);
    
    // 默认泛型类型参数
    struct Millimeters(u32);
    struct Meters(u32);
    
    impl Add<Meters> for Millimeters {
        type Output = Millimeters;
        
        fn add(self, other: Meters) -> Millimeters {
            Millimeters(self.0 + (other.0 * 1000))
        }
    }
    
    let mm = Millimeters(1000);
    let m = Meters(1);
    let result = mm + m;
    println!("    1000mm + 1m = {}mm", result.0);
}

fn fully_qualified_syntax() {
    println!("  完全限定语法:");
    
    trait Pilot {
        fn fly(&self);
    }
    
    trait Wizard {
        fn fly(&self);
    }
    
    struct Human;
    
    impl Pilot for Human {
        fn fly(&self) {
            println!("    作为飞行员飞行");
        }
    }
    
    impl Wizard for Human {
        fn fly(&self) {
            println!("    作为巫师飞行");
        }
    }
    
    impl Human {
        fn fly(&self) {
            println!("    作为人类飞行（挥动手臂）");
        }
    }
    
    let person = Human;
    
    // 调用不同的 fly 方法
    person.fly(); // 默认调用 Human 的方法
    Pilot::fly(&person); // 调用 Pilot trait 的方法
    Wizard::fly(&person); // 调用 Wizard trait 的方法
    
    // 关联函数的完全限定语法
    trait Animal {
        fn baby_name() -> String;
    }
    
    struct Dog;
    
    impl Dog {
        fn baby_name() -> String {
            String::from("小狗")
        }
    }
    
    impl Animal for Dog {
        fn baby_name() -> String {
            String::from("幼犬")
        }
    }
    
    println!("    狗的幼崽叫做 {}", Dog::baby_name());
    println!("    动物的幼崽叫做 {}", <Dog as Animal>::baby_name());
}

fn supertraits() {
    println!("  超 trait:");
    
    use std::fmt;
    
    trait OutlinePrint: fmt::Display {
        fn outline_print(&self) {
            let output = self.to_string();
            let len = output.len();
            println!("    {}", "*".repeat(len + 4));
            println!("    *{}*", " ".repeat(len + 2));
            println!("    * {} *", output);
            println!("    *{}*", " ".repeat(len + 2));
            println!("    {}", "*".repeat(len + 4));
        }
    }
    
    struct Point {
        x: i32,
        y: i32,
    }
    
    impl fmt::Display for Point {
        fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
            write!(f, "({}, {})", self.x, self.y)
        }
    }
    
    impl OutlinePrint for Point {}
    
    let point = Point { x: 1, y: 3 };
    point.outline_print();
}

fn newtype_pattern() {
    println!("  newtype 模式:");
    
    use std::fmt;
    
    struct Wrapper(Vec<String>);
    
    impl fmt::Display for Wrapper {
        fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
            write!(f, "[{}]", self.0.join(", "))
        }
    }
    
    let w = Wrapper(vec![String::from("hello"), String::from("world")]);
    println!("    包装器: {}", w);
    
    // newtype 模式允许我们为外部类型实现外部 trait
    // 绕过孤儿规则
}

/*
关键要点总结：

1. Trait 基础：
   - 定义共同行为
   - 类似接口的概念
   - 可以有默认实现

2. Trait 作为参数：
   - impl Trait 语法
   - Trait bound 语法
   - 多重约束

3. 返回 Trait：
   - impl Trait 返回类型
   - trait 对象 (dyn Trait)
   - 动态分发

4. 高级特性：
   - 关联类型
   - 运算符重载
   - 完全限定语法
   - 超 trait
   - newtype 模式

5. 设计原则：
   - 孤儿规则：只能为自己的类型或自己的 trait 实现
   - 一致性：避免冲突的实现
   - 零成本抽象：编译时优化

编译和运行：
cargo run --bin 13_traits
*/
