// 08_enums_and_pattern_matching.rs
// Rust 枚举和模式匹配详解

fn main() {
    println!("=== Rust 枚举和模式匹配 ===\n");

    // 1. 枚举基础
    enum_basics();
    
    // 2. 带数据的枚举
    enums_with_data();
    
    // 3. Option 枚举
    option_enum();
    
    // 4. match 表达式
    match_expressions();
    
    // 5. if let 语法
    if_let_syntax();
}

// 1. 枚举基础
fn enum_basics() {
    println!("1. 枚举基础:");
    
    // 定义简单枚举
    #[derive(Debug)]
    enum IpAddrKind {
        V4,
        V6,
    }
    
    // 创建枚举值
    let four = IpAddrKind::V4;
    let six = IpAddrKind::V6;
    
    println!("  IPv4: {:?}", four);
    println!("  IPv6: {:?}", six);
    
    // 枚举作为函数参数
    route(IpAddrKind::V4);
    route(IpAddrKind::V6);
    
    // 枚举与结构体结合
    struct IpAddr {
        kind: IpAddrKind,
        address: String,
    }
    
    let home = IpAddr {
        kind: IpAddrKind::V4,
        address: String::from("127.0.0.1"),
    };
    
    let loopback = IpAddr {
        kind: IpAddrKind::V6,
        address: String::from("::1"),
    };
    
    println!("  家庭地址: {:?} - {}", home.kind, home.address);
    println!("  回环地址: {:?} - {}", loopback.kind, loopback.address);
    
    println!();
}

fn route(ip_kind: IpAddrKind) {
    println!("    路由到 {:?} 地址", ip_kind);
}

// 2. 带数据的枚举
fn enums_with_data() {
    println!("2. 带数据的枚举:");
    
    // 枚举变体可以包含数据
    #[derive(Debug)]
    enum IpAddr {
        V4(u8, u8, u8, u8),
        V6(String),
    }
    
    let home = IpAddr::V4(127, 0, 0, 1);
    let loopback = IpAddr::V6(String::from("::1"));
    
    println!("  IPv4 地址: {:?}", home);
    println!("  IPv6 地址: {:?}", loopback);
    
    // 更复杂的枚举
    #[derive(Debug)]
    enum Message {
        Quit,                       // 无数据
        Move { x: i32, y: i32 },   // 命名字段
        Write(String),              // 单个字符串
        ChangeColor(i32, i32, i32), // 三个整数
    }
    
    let messages = vec![
        Message::Quit,
        Message::Move { x: 10, y: 20 },
        Message::Write(String::from("Hello")),
        Message::ChangeColor(255, 0, 0),
    ];
    
    for msg in messages {
        println!("  消息: {:?}", msg);
    }
    
    // 为枚举实现方法
    impl Message {
        fn call(&self) {
            match self {
                Message::Quit => println!("    执行退出"),
                Message::Move { x, y } => println!("    移动到 ({}, {})", x, y),
                Message::Write(text) => println!("    写入: {}", text),
                Message::ChangeColor(r, g, b) => println!("    改变颜色到 RGB({}, {}, {})", r, g, b),
            }
        }
    }
    
    let msg = Message::Write(String::from("hello"));
    msg.call();
    
    println!();
}

// 3. Option 枚举
fn option_enum() {
    println!("3. Option 枚举:");
    
    // Option 是 Rust 标准库中的枚举，用于表示可能为空的值
    // enum Option<T> {
    //     None,
    //     Some(T),
    // }
    
    let some_number = Some(5);
    let some_string = Some("a string");
    let absent_number: Option<i32> = None;
    
    println!("  有值的数字: {:?}", some_number);
    println!("  有值的字符串: {:?}", some_string);
    println!("  空值: {:?}", absent_number);
    
    // Option 的常用方法
    demonstrate_option_methods();
    
    // 处理 Option 值
    handle_option_values();
    
    println!();
}

fn demonstrate_option_methods() {
    println!("  Option 的常用方法:");
    
    let x = Some(2);
    let y: Option<i32> = None;
    
    // is_some() 和 is_none()
    println!("    x.is_some(): {}", x.is_some());
    println!("    y.is_none(): {}", y.is_none());
    
    // unwrap() - 获取值，如果是 None 会 panic
    println!("    x.unwrap(): {}", x.unwrap());
    // println!("y.unwrap(): {}", y.unwrap()); // 这会 panic
    
    // unwrap_or() - 提供默认值
    println!("    y.unwrap_or(0): {}", y.unwrap_or(0));
    
    // map() - 转换 Some 中的值
    let doubled = x.map(|val| val * 2);
    println!("    x.map(|val| val * 2): {:?}", doubled);
    
    // and_then() - 链式操作
    let result = x.and_then(|val| if val > 1 { Some(val * 2) } else { None });
    println!("    链式操作结果: {:?}", result);
}

fn handle_option_values() {
    println!("  处理 Option 值:");
    
    let numbers = vec![Some(1), None, Some(3), Some(4), None];
    
    for num in numbers {
        match num {
            Some(value) => println!("    找到值: {}", value),
            None => println!("    没有值"),
        }
    }
    
    // 使用 if let 处理 Option
    let config_max = Some(3u8);
    if let Some(max) = config_max {
        println!("    配置的最大值是 {}", max);
    }
}

// 4. match 表达式
fn match_expressions() {
    println!("4. match 表达式:");
    
    // 基本 match
    basic_match();
    
    // 绑定值的模式
    binding_patterns();
    
    // 匹配守卫
    match_guards();
    
    // @ 绑定
    at_bindings();
    
    println!();
}

fn basic_match() {
    println!("  基本 match:");
    
    #[derive(Debug)]
    enum Coin {
        Penny,
        Nickel,
        Dime,
        Quarter,
    }
    
    fn value_in_cents(coin: Coin) -> u8 {
        match coin {
            Coin::Penny => {
                println!("    幸运便士!");
                1
            }
            Coin::Nickel => 5,
            Coin::Dime => 10,
            Coin::Quarter => 25,
        }
    }
    
    let coin = Coin::Penny;
    println!("    {:?} 值 {} 分", coin, value_in_cents(coin));
}

fn binding_patterns() {
    println!("  绑定值的模式:");
    
    #[derive(Debug)]
    enum UsState {
        Alabama,
        Alaska,
        // ... 其他州
    }
    
    #[derive(Debug)]
    enum Coin {
        Penny,
        Nickel,
        Dime,
        Quarter(UsState),
    }
    
    fn value_in_cents(coin: Coin) -> u8 {
        match coin {
            Coin::Penny => 1,
            Coin::Nickel => 5,
            Coin::Dime => 10,
            Coin::Quarter(state) => {
                println!("    来自 {:?} 州的 25 分硬币!", state);
                25
            }
        }
    }
    
    let coin = Coin::Quarter(UsState::Alaska);
    println!("    值: {} 分", value_in_cents(coin));
}

fn match_guards() {
    println!("  匹配守卫:");
    
    let num = Some(4);
    
    match num {
        Some(x) if x < 5 => println!("    小于 5: {}", x),
        Some(x) => println!("    大于等于 5: {}", x),
        None => println!("    没有值"),
    }
    
    // 复杂的守卫条件
    let x = 4;
    let y = false;
    
    match x {
        4 | 5 | 6 if y => println!("    是的"),
        _ => println!("    不是"),
    }
}

fn at_bindings() {
    println!("  @ 绑定:");
    
    enum Message {
        Hello { id: i32 },
    }
    
    let msg = Message::Hello { id: 5 };
    
    match msg {
        Message::Hello {
            id: id_variable @ 3..=7,
        } => println!("    找到 id 在范围内: {}", id_variable),
        Message::Hello { id: 10..=12 } => {
            println!("    找到 id 在另一个范围内");
        }
        Message::Hello { id } => println!("    找到其他 id: {}", id),
    }
}

// 5. if let 语法
fn if_let_syntax() {
    println!("5. if let 语法:");
    
    // if let 是 match 的语法糖
    let config_max = Some(3u8);
    
    // 使用 match
    match config_max {
        Some(max) => println!("  match: 最大值是 {}", max),
        _ => (),
    }
    
    // 使用 if let (更简洁)
    if let Some(max) = config_max {
        println!("  if let: 最大值是 {}", max);
    }
    
    // if let 与 else
    let favorite_color: Option<&str> = None;
    let is_tuesday = false;
    let age: Result<u8, _> = "34".parse();
    
    if let Some(color) = favorite_color {
        println!("  使用你最喜欢的颜色 {} 作为背景", color);
    } else if is_tuesday {
        println!("  星期二是绿色的日子!");
    } else if let Ok(age) = age {
        if age > 30 {
            println!("  使用紫色作为背景颜色");
        } else {
            println!("  使用橙色作为背景颜色");
        }
    } else {
        println!("  使用蓝色作为背景颜色");
    }
    
    // while let 循环
    let mut stack = Vec::new();
    stack.push(1);
    stack.push(2);
    stack.push(3);
    
    println!("  while let 弹出栈:");
    while let Some(top) = stack.pop() {
        println!("    {}", top);
    }
    
    println!();
}

// 高级模式匹配示例
#[allow(dead_code)]
fn advanced_pattern_matching() {
    println!("高级模式匹配:");
    
    // 解构结构体
    struct Point {
        x: i32,
        y: i32,
    }
    
    let p = Point { x: 0, y: 7 };
    
    match p {
        Point { x, y: 0 } => println!("  在 x 轴上，x = {}", x),
        Point { x: 0, y } => println!("  在 y 轴上，y = {}", y),
        Point { x, y } => println!("  在其他位置: ({}, {})", x, y),
    }
    
    // 解构枚举
    enum Color {
        Rgb(i32, i32, i32),
        Hsv(i32, i32, i32),
    }
    
    enum Message {
        Quit,
        Move { x: i32, y: i32 },
        Write(String),
        ChangeColor(Color),
    }
    
    let msg = Message::ChangeColor(Color::Hsv(0, 160, 255));
    
    match msg {
        Message::ChangeColor(Color::Rgb(r, g, b)) => {
            println!("  改变颜色到红色 {}，绿色 {}，蓝色 {}", r, g, b);
        }
        Message::ChangeColor(Color::Hsv(h, s, v)) => {
            println!("  改变颜色到色调 {}，饱和度 {}，明度 {}", h, s, v);
        }
        _ => (),
    }
    
    // 忽略模式
    let numbers = (2, 4, 8, 16, 32);
    
    match numbers {
        (first, _, third, _, fifth) => {
            println!("  一些数字: {}, {}, {}", first, third, fifth);
        }
    }
}

/*
关键要点总结：

1. 枚举定义：
   - 使用 enum 关键字
   - 变体可以包含不同类型的数据
   - 可以为枚举实现方法

2. Option 枚举：
   - 表示可能为空的值
   - Some(T) 和 None 两个变体
   - 提供丰富的方法处理可选值

3. match 表达式：
   - 必须穷尽所有可能
   - 支持模式匹配和值绑定
   - 支持守卫条件和 @ 绑定

4. if let 语法：
   - match 的简化形式
   - 只关心一个模式时使用
   - 可以与 else 结合

5. 模式匹配的优势：
   - 编译时检查完整性
   - 类型安全
   - 表达力强

编译和运行：
cargo run --bin 08_enums_and_pattern_matching
*/
