// 11_error_handling.rs
// Rust 错误处理详解

use std::fs::File;
use std::io::{self, Read, ErrorKind};

fn main() {
    println!("=== Rust 错误处理 ===\n");

    // 1. panic! 和不可恢复错误
    panic_and_unrecoverable_errors();
    
    // 2. Result 和可恢复错误
    result_and_recoverable_errors();
    
    // 3. 错误传播
    error_propagation();
    
    // 4. ? 运算符
    question_mark_operator();
    
    // 5. 自定义错误类型
    custom_error_types();
}

// 1. panic! 和不可恢复错误
fn panic_and_unrecoverable_errors() {
    println!("1. panic! 和不可恢复错误:");
    
    // panic! 宏会导致程序崩溃
    // panic!("程序崩溃了！");
    
    // 数组越界会导致 panic
    // let v = vec![1, 2, 3];
    // v[99]; // 这会导致 panic
    
    // 使用 RUST_BACKTRACE 环境变量可以看到调用栈
    println!("  设置 RUST_BACKTRACE=1 可以看到详细的错误信息");
    
    // 在某些情况下，panic 是合适的：
    // - 示例、原型代码、测试
    // - 当你确信代码不会失败时
    // - 当继续执行可能是有害的时
    
    println!("  panic! 用于不可恢复的错误");
    println!("  程序会立即终止");
    
    println!();
}

// 2. Result 和可恢复错误
fn result_and_recoverable_errors() {
    println!("2. Result 和可恢复错误:");
    
    // Result 枚举定义：
    // enum Result<T, E> {
    //     Ok(T),
    //     Err(E),
    // }
    
    // 处理文件打开错误
    handle_file_open_error();
    
    // 使用 match 处理 Result
    match_result_handling();
    
    // Result 的便捷方法
    result_convenience_methods();
    
    println!();
}

fn handle_file_open_error() {
    println!("  处理文件打开错误:");
    
    let filename = "hello.txt";
    let greeting_file_result = File::open(filename);
    
    let _greeting_file = match greeting_file_result {
        Ok(file) => {
            println!("    文件打开成功");
            file
        }
        Err(error) => {
            println!("    文件打开失败: {}", error);
            return; // 提前返回，避免 panic
        }
    };
}

fn match_result_handling() {
    println!("  使用 match 处理不同类型的错误:");
    
    let filename = "hello.txt";
    let greeting_file_result = File::open(filename);
    
    let _greeting_file = match greeting_file_result {
        Ok(file) => file,
        Err(error) => match error.kind() {
            ErrorKind::NotFound => {
                println!("    文件不存在，尝试创建");
                match File::create(filename) {
                    Ok(fc) => {
                        println!("    文件创建成功");
                        fc
                    }
                    Err(e) => {
                        println!("    文件创建失败: {}", e);
                        return;
                    }
                }
            }
            other_error => {
                println!("    其他错误: {:?}", other_error);
                return;
            }
        }
    };
}

fn result_convenience_methods() {
    println!("  Result 的便捷方法:");
    
    let filename = "hello.txt";
    
    // unwrap：如果是 Ok 返回值，如果是 Err 则 panic
    // let file = File::open(filename).unwrap();
    
    // expect：类似 unwrap，但可以自定义错误消息
    // let file = File::open(filename).expect("无法打开文件");
    
    // unwrap_or：提供默认值
    let file_result = File::open(filename);
    let _file_or_default = file_result.unwrap_or_else(|error| {
        println!("    使用默认处理: {}", error);
        File::create("default.txt").unwrap_or_else(|_| {
            panic!("无法创建默认文件");
        })
    });
    
    // is_ok 和 is_err
    let result = File::open(filename);
    if result.is_ok() {
        println!("    文件打开成功");
    } else {
        println!("    文件打开失败");
    }
    
    // map 和 map_err
    let result = File::open(filename)
        .map(|_| "文件打开成功")
        .map_err(|e| format!("文件打开失败: {}", e));
    
    match result {
        Ok(msg) => println!("    {}", msg),
        Err(msg) => println!("    {}", msg),
    }
}

// 3. 错误传播
fn error_propagation() {
    println!("3. 错误传播:");
    
    // 手动传播错误
    match read_username_from_file_manual() {
        Ok(username) => println!("  用户名: {}", username),
        Err(e) => println!("  读取用户名失败: {}", e),
    }
    
    // 使用 ? 运算符传播错误
    match read_username_from_file_with_question_mark() {
        Ok(username) => println!("  用户名: {}", username),
        Err(e) => println!("  读取用户名失败: {}", e),
    }
    
    println!();
}

fn read_username_from_file_manual() -> Result<String, io::Error> {
    let username_file_result = File::open("username.txt");
    
    let mut username_file = match username_file_result {
        Ok(file) => file,
        Err(e) => return Err(e), // 传播错误
    };
    
    let mut username = String::new();
    
    match username_file.read_to_string(&mut username) {
        Ok(_) => Ok(username),
        Err(e) => Err(e), // 传播错误
    }
}

fn read_username_from_file_with_question_mark() -> Result<String, io::Error> {
    let mut username_file = File::open("username.txt")?; // ? 自动传播错误
    let mut username = String::new();
    username_file.read_to_string(&mut username)?; // ? 自动传播错误
    Ok(username)
}

// 4. ? 运算符
fn question_mark_operator() {
    println!("4. ? 运算符:");
    
    // ? 运算符的简化版本
    match read_username_simplified() {
        Ok(username) => println!("  简化版用户名: {}", username),
        Err(e) => println!("  读取失败: {}", e),
    }
    
    // ? 运算符与 Option
    option_with_question_mark();
    
    // ? 运算符的类型转换
    type_conversion_with_question_mark();
    
    println!();
}

fn read_username_simplified() -> Result<String, io::Error> {
    let mut username = String::new();
    File::open("username.txt")?.read_to_string(&mut username)?;
    Ok(username)
}

fn option_with_question_mark() {
    println!("  ? 运算符与 Option:");
    
    fn last_char_of_first_line(text: &str) -> Option<char> {
        text.lines().next()?.chars().last()
    }
    
    let text = "Hello\nWorld";
    match last_char_of_first_line(text) {
        Some(ch) => println!("    最后一个字符: {}", ch),
        None => println!("    没有找到字符"),
    }
}

fn type_conversion_with_question_mark() {
    println!("  ? 运算符的类型转换:");
    
    // ? 运算符会自动调用 From trait 进行类型转换
    fn read_number_from_file() -> Result<i32, Box<dyn std::error::Error>> {
        let mut file = File::open("number.txt")?;
        let mut contents = String::new();
        file.read_to_string(&mut contents)?;
        let number: i32 = contents.trim().parse()?; // 字符串解析错误也会被转换
        Ok(number)
    }
    
    match read_number_from_file() {
        Ok(number) => println!("    读取的数字: {}", number),
        Err(e) => println!("    读取数字失败: {}", e),
    }
}

// 5. 自定义错误类型
fn custom_error_types() {
    println!("5. 自定义错误类型:");
    
    // 使用自定义错误
    match divide_custom(10, 0) {
        Ok(result) => println!("  除法结果: {}", result),
        Err(e) => println!("  除法错误: {}", e),
    }
    
    // 使用枚举错误类型
    match parse_and_double("abc") {
        Ok(result) => println!("  解析并翻倍: {}", result),
        Err(e) => println!("  解析错误: {:?}", e),
    }
    
    println!();
}

// 简单的自定义错误类型
#[derive(Debug)]
struct DivisionByZeroError;

impl std::fmt::Display for DivisionByZeroError {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        write!(f, "除数不能为零")
    }
}

impl std::error::Error for DivisionByZeroError {}

fn divide_custom(a: f64, b: f64) -> Result<f64, DivisionByZeroError> {
    if b == 0.0 {
        Err(DivisionByZeroError)
    } else {
        Ok(a / b)
    }
}

// 使用枚举的错误类型
#[derive(Debug)]
enum ParseError {
    InvalidNumber,
    NegativeNumber,
}

impl std::fmt::Display for ParseError {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        match self {
            ParseError::InvalidNumber => write!(f, "无效的数字格式"),
            ParseError::NegativeNumber => write!(f, "数字不能为负数"),
        }
    }
}

impl std::error::Error for ParseError {}

fn parse_and_double(input: &str) -> Result<i32, ParseError> {
    let number: i32 = input.parse().map_err(|_| ParseError::InvalidNumber)?;
    
    if number < 0 {
        return Err(ParseError::NegativeNumber);
    }
    
    Ok(number * 2)
}

// 错误处理的最佳实践
#[allow(dead_code)]
fn error_handling_best_practices() {
    println!("错误处理最佳实践:");
    
    // 1. 使用 Result 而不是 panic，除非真的是不可恢复的错误
    // 2. 为库代码返回 Result，让调用者决定如何处理
    // 3. 在应用程序代码中，可以使用 unwrap 或 expect（如果确信不会失败）
    // 4. 使用 ? 运算符简化错误传播
    // 5. 创建有意义的错误类型
    // 6. 在错误消息中提供足够的上下文信息
    
    println!("  1. 优先使用 Result 而不是 panic");
    println!("  2. 库代码返回 Result，应用代码可以 unwrap");
    println!("  3. 使用 ? 运算符简化错误传播");
    println!("  4. 创建有意义的自定义错误类型");
    println!("  5. 提供足够的错误上下文信息");
}

// 错误处理的高级技巧
#[allow(dead_code)]
fn advanced_error_handling() {
    use std::error::Error;
    
    // 错误链
    fn process_file(filename: &str) -> Result<String, Box<dyn Error>> {
        let mut file = File::open(filename)
            .map_err(|e| format!("无法打开文件 '{}': {}", filename, e))?;
        
        let mut contents = String::new();
        file.read_to_string(&mut contents)
            .map_err(|e| format!("无法读取文件 '{}': {}", filename, e))?;
        
        Ok(contents)
    }
    
    // 使用 anyhow crate（需要添加依赖）
    // use anyhow::{Context, Result};
    // 
    // fn process_file_anyhow(filename: &str) -> Result<String> {
    //     let mut file = File::open(filename)
    //         .with_context(|| format!("无法打开文件 '{}'", filename))?;
    //     
    //     let mut contents = String::new();
    //     file.read_to_string(&mut contents)
    //         .with_context(|| format!("无法读取文件 '{}'", filename))?;
    //     
    //     Ok(contents)
    // }
    
    println!("高级错误处理技巧:");
    println!("  - 错误链：保留原始错误信息");
    println!("  - 使用 anyhow 或 thiserror crate");
    println!("  - 为不同的错误层级提供不同的处理策略");
}

/*
关键要点总结：

1. 错误分类：
   - 不可恢复错误：使用 panic!
   - 可恢复错误：使用 Result<T, E>

2. Result 枚举：
   - Ok(T)：成功值
   - Err(E)：错误值
   - 提供丰富的方法处理错误

3. 错误传播：
   - 手动传播：match 和 return
   - 自动传播：? 运算符

4. ? 运算符：
   - 简化错误传播
   - 自动类型转换
   - 适用于 Result 和 Option

5. 自定义错误：
   - 实现 Display 和 Error trait
   - 使用枚举表示多种错误类型
   - 提供有意义的错误信息

6. 最佳实践：
   - 库代码返回 Result
   - 应用代码可以选择 panic
   - 使用 ? 运算符
   - 创建有意义的错误类型

编译和运行：
cargo run --bin 11_error_handling
*/
