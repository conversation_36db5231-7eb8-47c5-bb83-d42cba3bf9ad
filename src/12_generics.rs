// 12_generics.rs
// Rust 泛型详解

fn main() {
    println!("=== Rust 泛型 ===\n");

    // 1. 泛型函数
    generic_functions();
    
    // 2. 泛型结构体
    generic_structs();
    
    // 3. 泛型枚举
    generic_enums();
    
    // 4. 泛型方法
    generic_methods();
    
    // 5. 泛型约束
    generic_constraints();
}

// 1. 泛型函数
fn generic_functions() {
    println!("1. 泛型函数:");
    
    // 不使用泛型的重复代码
    fn largest_i32(list: &[i32]) -> &i32 {
        let mut largest = &list[0];
        for item in list {
            if item > largest {
                largest = item;
            }
        }
        largest
    }
    
    fn largest_char(list: &[char]) -> &char {
        let mut largest = &list[0];
        for item in list {
            if item > largest {
                largest = item;
            }
        }
        largest
    }
    
    let number_list = vec![34, 50, 25, 100, 65];
    let result = largest_i32(&number_list);
    println!("  最大的数字是 {}", result);
    
    let char_list = vec!['y', 'm', 'a', 'q'];
    let result = largest_char(&char_list);
    println!("  最大的字符是 {}", result);
    
    // 使用泛型消除重复
    fn largest<T: PartialOrd>(list: &[T]) -> &T {
        let mut largest = &list[0];
        for item in list {
            if item > largest {
                largest = item;
            }
        }
        largest
    }
    
    let number_list = vec![34, 50, 25, 100, 65];
    let result = largest(&number_list);
    println!("  泛型函数 - 最大的数字是 {}", result);
    
    let char_list = vec!['y', 'm', 'a', 'q'];
    let result = largest(&char_list);
    println!("  泛型函数 - 最大的字符是 {}", result);
    
    // 多个泛型参数
    fn compare_and_display<T, U>(t: T, u: U) 
    where
        T: std::fmt::Display + PartialOrd,
        U: std::fmt::Display,
    {
        println!("  比较 {} 和 {}", t, u);
    }
    
    compare_and_display(5, "hello");
    compare_and_display(3.14, 42);
    
    println!();
}

// 2. 泛型结构体
fn generic_structs() {
    println!("2. 泛型结构体:");
    
    // 单个泛型参数
    #[derive(Debug)]
    struct Point<T> {
        x: T,
        y: T,
    }
    
    let integer_point = Point { x: 5, y: 10 };
    let float_point = Point { x: 1.0, y: 4.0 };
    
    println!("  整数点: {:?}", integer_point);
    println!("  浮点点: {:?}", float_point);
    
    // 多个泛型参数
    #[derive(Debug)]
    struct MixedPoint<T, U> {
        x: T,
        y: U,
    }
    
    let mixed_point = MixedPoint { x: 5, y: 4.0 };
    println!("  混合点: {:?}", mixed_point);
    
    // 为泛型结构体实现方法
    impl<T> Point<T> {
        fn x(&self) -> &T {
            &self.x
        }
        
        fn y(&self) -> &T {
            &self.y
        }
    }
    
    let p = Point { x: 5, y: 10 };
    println!("  点的 x 坐标: {}", p.x());
    
    // 为特定类型实现方法
    impl Point<f32> {
        fn distance_from_origin(&self) -> f32 {
            (self.x.powi(2) + self.y.powi(2)).sqrt()
        }
    }
    
    let float_point = Point { x: 3.0, y: 4.0 };
    println!("  到原点的距离: {}", float_point.distance_from_origin());
    
    // 混合泛型方法
    impl<T, U> MixedPoint<T, U> {
        fn mixup<V, W>(self, other: MixedPoint<V, W>) -> MixedPoint<T, W> {
            MixedPoint {
                x: self.x,
                y: other.y,
            }
        }
    }
    
    let p1 = MixedPoint { x: 5, y: 10.4 };
    let p2 = MixedPoint { x: "Hello", y: 'c' };
    let p3 = p1.mixup(p2);
    println!("  混合后的点: x = {}, y = {}", p3.x, p3.y);
    
    println!();
}

// 3. 泛型枚举
fn generic_enums() {
    println!("3. 泛型枚举:");
    
    // Option 枚举（标准库中的例子）
    // enum Option<T> {
    //     Some(T),
    //     None,
    // }
    
    let some_number = Some(5);
    let some_string = Some("a string");
    let absent_number: Option<i32> = None;
    
    println!("  Some 数字: {:?}", some_number);
    println!("  Some 字符串: {:?}", some_string);
    println!("  None: {:?}", absent_number);
    
    // Result 枚举（标准库中的例子）
    // enum Result<T, E> {
    //     Ok(T),
    //     Err(E),
    // }
    
    let success: Result<i32, &str> = Ok(42);
    let failure: Result<i32, &str> = Err("出错了");
    
    println!("  成功结果: {:?}", success);
    println!("  失败结果: {:?}", failure);
    
    // 自定义泛型枚举
    #[derive(Debug)]
    enum Message<T> {
        Quit,
        Move { x: T, y: T },
        Write(String),
        ChangeColor(T, T, T),
    }
    
    let int_message = Message::Move { x: 10, y: 20 };
    let float_message = Message::ChangeColor(1.0, 0.5, 0.8);
    
    println!("  整数消息: {:?}", int_message);
    println!("  浮点消息: {:?}", float_message);
    
    // 为泛型枚举实现方法
    impl<T> Message<T> 
    where 
        T: std::fmt::Display,
    {
        fn describe(&self) {
            match self {
                Message::Quit => println!("    退出消息"),
                Message::Move { x, y } => println!("    移动到 ({}, {})", x, y),
                Message::Write(text) => println!("    写入: {}", text),
                Message::ChangeColor(r, g, b) => println!("    改变颜色到 ({}, {}, {})", r, g, b),
            }
        }
    }
    
    int_message.describe();
    float_message.describe();
    
    println!();
}

// 4. 泛型方法
fn generic_methods() {
    println!("4. 泛型方法:");
    
    struct Container<T> {
        value: T,
    }
    
    impl<T> Container<T> {
        fn new(value: T) -> Self {
            Container { value }
        }
        
        fn get(&self) -> &T {
            &self.value
        }
        
        fn set(&mut self, value: T) {
            self.value = value;
        }
        
        // 泛型方法
        fn map<U, F>(self, f: F) -> Container<U>
        where
            F: FnOnce(T) -> U,
        {
            Container {
                value: f(self.value),
            }
        }
    }
    
    let mut container = Container::new(42);
    println!("  容器值: {}", container.get());
    
    container.set(100);
    println!("  设置后的值: {}", container.get());
    
    // 使用泛型方法转换类型
    let string_container = container.map(|x| x.to_string());
    println!("  转换后的容器: {}", string_container.get());
    
    // 链式调用
    let result = Container::new(5)
        .map(|x| x * 2)
        .map(|x| format!("结果: {}", x));
    println!("  链式调用结果: {}", result.get());
    
    println!();
}

// 5. 泛型约束
fn generic_constraints() {
    println!("5. 泛型约束:");
    
    // trait 约束
    trait_bounds();
    
    // where 子句
    where_clauses();
    
    // 关联类型
    associated_types();
    
    println!();
}

fn trait_bounds() {
    println!("  trait 约束:");
    
    // 使用 trait 约束限制泛型类型
    fn print_and_compare<T: std::fmt::Display + PartialOrd>(a: T, b: T) {
        println!("    比较 {} 和 {}", a, b);
        if a > b {
            println!("    {} 更大", a);
        } else if a < b {
            println!("    {} 更小", a);
        } else {
            println!("    它们相等");
        }
    }
    
    print_and_compare(5, 10);
    print_and_compare("hello", "world");
    
    // 多个 trait 约束
    fn process<T>(item: T) 
    where 
        T: std::fmt::Debug + Clone + PartialEq,
    {
        let cloned = item.clone();
        println!("    原始: {:?}", item);
        println!("    克隆: {:?}", cloned);
        println!("    相等: {}", item == cloned);
    }
    
    process(42);
    process("hello".to_string());
}

fn where_clauses() {
    println!("  where 子句:");
    
    // 复杂的 trait 约束使用 where 子句更清晰
    fn complex_function<T, U, V>(t: T, u: U) -> V
    where
        T: std::fmt::Display + Clone,
        U: Clone + std::fmt::Debug,
        V: From<T>,
    {
        println!("    T: {}", t);
        println!("    U: {:?}", u);
        V::from(t)
    }
    
    let result: String = complex_function("hello", vec![1, 2, 3]);
    println!("    结果: {}", result);
    
    // 条件实现
    struct Pair<T> {
        x: T,
        y: T,
    }
    
    impl<T> Pair<T> {
        fn new(x: T, y: T) -> Self {
            Self { x, y }
        }
    }
    
    // 只为实现了特定 trait 的类型实现方法
    impl<T: std::fmt::Display + PartialOrd> Pair<T> {
        fn cmp_display(&self) {
            if self.x >= self.y {
                println!("    最大的成员是 x = {}", self.x);
            } else {
                println!("    最大的成员是 y = {}", self.y);
            }
        }
    }
    
    let pair = Pair::new(10, 20);
    pair.cmp_display();
}

fn associated_types() {
    println!("  关联类型:");
    
    // 定义带有关联类型的 trait
    trait Iterator {
        type Item;
        
        fn next(&mut self) -> Option<Self::Item>;
    }
    
    // 实现 trait
    struct Counter {
        current: usize,
        max: usize,
    }
    
    impl Counter {
        fn new(max: usize) -> Counter {
            Counter { current: 0, max }
        }
    }
    
    impl Iterator for Counter {
        type Item = usize;
        
        fn next(&mut self) -> Option<Self::Item> {
            if self.current < self.max {
                let current = self.current;
                self.current += 1;
                Some(current)
            } else {
                None
            }
        }
    }
    
    let mut counter = Counter::new(3);
    while let Some(value) = counter.next() {
        println!("    计数器值: {}", value);
    }
}

// 泛型的性能
#[allow(dead_code)]
fn generic_performance() {
    println!("泛型的性能:");
    
    // Rust 使用单态化（monomorphization）
    // 编译时为每个具体类型生成专门的代码
    // 运行时没有额外开销
    
    let integer = Some(5);
    let float = Some(5.0);
    
    // 编译器会生成：
    // enum Option_i32 {
    //     Some(i32),
    //     None,
    // }
    // 
    // enum Option_f64 {
    //     Some(f64),
    //     None,
    // }
    
    println!("  泛型在运行时没有性能开销");
    println!("  编译器进行单态化优化");
    println!("  integer: {:?}, float: {:?}", integer, float);
}

/*
关键要点总结：

1. 泛型函数：
   - 使用 <T> 声明类型参数
   - 可以有多个类型参数
   - 支持 trait 约束

2. 泛型结构体：
   - 字段可以使用泛型类型
   - 可以为泛型结构体实现方法
   - 可以为特定类型实现专门的方法

3. 泛型枚举：
   - 变体可以包含泛型类型
   - Option 和 Result 是常见例子

4. 泛型方法：
   - 方法可以有自己的泛型参数
   - 支持类型转换和链式调用

5. 泛型约束：
   - trait 约束限制泛型类型
   - where 子句提供更清晰的语法
   - 关联类型简化复杂的泛型关系

6. 性能：
   - 编译时单态化
   - 运行时零开销
   - 类型安全

编译和运行：
cargo run --bin 12_generics
*/
