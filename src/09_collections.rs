// 09_collections.rs
// Rust 常见集合及操作详解

use std::collections::HashMap;

fn main() {
    println!("=== Rust 常见集合 ===\n");

    // 1. 动态数组 Vec<T>
    vectors();
    
    // 2. 字符串 String
    strings();
    
    // 3. 哈希映射 HashMap<K, V>
    hash_maps();
    
    // 4. 其他集合类型
    other_collections();
}

// 1. 动态数组 Vec<T>
fn vectors() {
    println!("1. 动态数组 Vec<T>:");
    
    // 创建向量
    vector_creation();
    
    // 向量操作
    vector_operations();
    
    // 遍历向量
    vector_iteration();
    
    // 使用枚举存储多种类型
    vector_with_enums();
    
    println!();
}

fn vector_creation() {
    println!("  创建向量:");
    
    // 创建空向量
    let mut v1: Vec<i32> = Vec::new();
    v1.push(1);
    v1.push(2);
    v1.push(3);
    println!("    空向量添加元素: {:?}", v1);
    
    // 使用 vec! 宏创建
    let v2 = vec![1, 2, 3];
    println!("    使用宏创建: {:?}", v2);
    
    // 创建指定容量的向量
    let mut v3 = Vec::with_capacity(10);
    println!("    指定容量向量的容量: {}", v3.capacity());
    v3.push(1);
    println!("    添加元素后: {:?}", v3);
    
    // 创建重复元素的向量
    let v4 = vec![0; 5]; // 5个0
    println!("    重复元素: {:?}", v4);
}

fn vector_operations() {
    println!("  向量操作:");
    
    let mut v = vec![1, 2, 3, 4, 5];
    println!("    原始向量: {:?}", v);
    
    // 添加元素
    v.push(6);
    println!("    添加元素后: {:?}", v);
    
    // 删除最后一个元素
    let last = v.pop();
    println!("    弹出的元素: {:?}", last);
    println!("    弹出后的向量: {:?}", v);
    
    // 访问元素
    let third = &v[2]; // 可能 panic
    println!("    第三个元素: {}", third);
    
    let third_safe = v.get(2); // 返回 Option
    match third_safe {
        Some(value) => println!("    安全访问第三个元素: {}", value),
        None => println!("    没有第三个元素"),
    }
    
    // 插入和删除
    v.insert(1, 10); // 在索引1处插入10
    println!("    插入后: {:?}", v);
    
    let removed = v.remove(1); // 删除索引1的元素
    println!("    删除的元素: {}", removed);
    println!("    删除后: {:?}", v);
    
    // 长度和容量
    println!("    长度: {}, 容量: {}", v.len(), v.capacity());
    
    // 清空向量
    v.clear();
    println!("    清空后: {:?}", v);
}

fn vector_iteration() {
    println!("  遍历向量:");
    
    let v = vec![100, 32, 57];
    
    // 不可变引用遍历
    print!("    不可变遍历: ");
    for i in &v {
        print!("{} ", i);
    }
    println!();
    
    // 可变引用遍历
    let mut v_mut = vec![100, 32, 57];
    print!("    可变遍历（每个元素+50）: ");
    for i in &mut v_mut {
        *i += 50;
        print!("{} ", i);
    }
    println!();
    
    // 获取所有权遍历
    print!("    所有权遍历: ");
    for i in v {
        print!("{} ", i);
    }
    println!();
    // v 现在不再可用
    
    // 使用迭代器方法
    let v2 = vec![1, 2, 3, 4, 5];
    let doubled: Vec<i32> = v2.iter().map(|x| x * 2).collect();
    println!("    映射结果: {:?}", doubled);
    
    let sum: i32 = v2.iter().sum();
    println!("    求和: {}", sum);
    
    let even_numbers: Vec<&i32> = v2.iter().filter(|&&x| x % 2 == 0).collect();
    println!("    偶数: {:?}", even_numbers);
}

fn vector_with_enums() {
    println!("  使用枚举存储多种类型:");
    
    #[derive(Debug)]
    enum SpreadsheetCell {
        Int(i32),
        Float(f64),
        Text(String),
    }
    
    let row = vec![
        SpreadsheetCell::Int(3),
        SpreadsheetCell::Text(String::from("blue")),
        SpreadsheetCell::Float(10.12),
    ];
    
    for cell in &row {
        match cell {
            SpreadsheetCell::Int(i) => println!("    整数: {}", i),
            SpreadsheetCell::Float(f) => println!("    浮点数: {}", f),
            SpreadsheetCell::Text(s) => println!("    文本: {}", s),
        }
    }
}

// 2. 字符串 String
fn strings() {
    println!("2. 字符串 String:");
    
    // 字符串创建
    string_creation();
    
    // 字符串操作
    string_operations();
    
    // 字符串索引和切片
    string_indexing_and_slicing();
    
    // 字符串遍历
    string_iteration();
    
    println!();
}

fn string_creation() {
    println!("  字符串创建:");
    
    // 创建空字符串
    let mut s1 = String::new();
    s1.push_str("hello");
    println!("    空字符串: {}", s1);
    
    // 从字符串字面量创建
    let s2 = "initial contents".to_string();
    println!("    to_string(): {}", s2);
    
    let s3 = String::from("initial contents");
    println!("    String::from(): {}", s3);
    
    // 从其他类型创建
    let number = 42;
    let s4 = number.to_string();
    println!("    数字转字符串: {}", s4);
    
    // 指定容量
    let mut s5 = String::with_capacity(10);
    println!("    指定容量: {}", s5.capacity());
    s5.push_str("hello");
    println!("    添加内容后: {}", s5);
}

fn string_operations() {
    println!("  字符串操作:");
    
    let mut s = String::from("foo");
    
    // 追加字符串
    s.push_str("bar");
    println!("    push_str: {}", s);
    
    // 追加字符
    s.push('!');
    println!("    push: {}", s);
    
    // 字符串连接
    let s1 = String::from("Hello, ");
    let s2 = String::from("world!");
    let s3 = s1 + &s2; // s1 被移动，不能再使用
    println!("    连接: {}", s3);
    
    // 使用 format! 宏
    let s1 = String::from("tic");
    let s2 = String::from("tac");
    let s3 = String::from("toe");
    let s = format!("{}-{}-{}", s1, s2, s3);
    println!("    format!: {}", s);
    
    // 替换
    let mut s = String::from("Hello, world!");
    s = s.replace("world", "Rust");
    println!("    替换: {}", s);
    
    // 大小写转换
    let s = String::from("Hello, World!");
    println!("    小写: {}", s.to_lowercase());
    println!("    大写: {}", s.to_uppercase());
    
    // 修剪空白
    let s = String::from("  hello world  ");
    println!("    修剪前: '{}'", s);
    println!("    修剪后: '{}'", s.trim());
}

fn string_indexing_and_slicing() {
    println!("  字符串索引和切片:");
    
    let s = String::from("hello");
    
    // Rust 不支持字符串索引
    // let h = s[0]; // 编译错误
    
    // 使用切片（需要小心字节边界）
    let hello = "Здравствуйте";
    let s = &hello[0..4]; // 取前4个字节
    println!("    切片: {}", s);
    
    // 安全的字符串处理
    let s = String::from("hello world");
    let hello = &s[0..5];
    let world = &s[6..11];
    println!("    hello: {}, world: {}", hello, world);
    
    // 字符串长度
    println!("    字节长度: {}", s.len());
    println!("    字符数量: {}", s.chars().count());
}

fn string_iteration() {
    println!("  字符串遍历:");
    
    let s = String::from("नमस्ते");
    
    // 按字符遍历
    print!("    字符: ");
    for c in s.chars() {
        print!("{} ", c);
    }
    println!();
    
    // 按字节遍历
    print!("    字节: ");
    for b in s.bytes() {
        print!("{} ", b);
    }
    println!();
    
    // 字符串分割
    let s = String::from("hello,world,rust");
    let parts: Vec<&str> = s.split(',').collect();
    println!("    分割: {:?}", parts);
}

// 3. 哈希映射 HashMap<K, V>
fn hash_maps() {
    println!("3. 哈希映射 HashMap<K, V>:");
    
    // 哈希映射创建
    hashmap_creation();
    
    // 哈希映射操作
    hashmap_operations();
    
    // 哈希映射遍历
    hashmap_iteration();
    
    // 哈希映射更新
    hashmap_updates();
    
    println!();
}

fn hashmap_creation() {
    println!("  哈希映射创建:");
    
    // 创建空哈希映射
    let mut scores = HashMap::new();
    scores.insert(String::from("Blue"), 10);
    scores.insert(String::from("Yellow"), 50);
    println!("    空映射添加元素: {:?}", scores);
    
    // 从向量创建
    let teams = vec![String::from("Blue"), String::from("Yellow")];
    let initial_scores = vec![10, 50];
    let scores: HashMap<_, _> = teams.into_iter().zip(initial_scores.into_iter()).collect();
    println!("    从向量创建: {:?}", scores);
    
    // 使用宏创建（需要外部 crate）
    // let scores = hashmap!{
    //     "Blue" => 10,
    //     "Yellow" => 50,
    // };
}

fn hashmap_operations() {
    println!("  哈希映射操作:");
    
    let mut scores = HashMap::new();
    scores.insert(String::from("Blue"), 10);
    scores.insert(String::from("Yellow"), 50);
    
    // 访问值
    let team_name = String::from("Blue");
    let score = scores.get(&team_name);
    match score {
        Some(s) => println!("    Blue 队得分: {}", s),
        None => println!("    Blue 队不存在"),
    }
    
    // 使用 unwrap_or 提供默认值
    let score = scores.get("Red").unwrap_or(&0);
    println!("    Red 队得分（默认）: {}", score);
    
    // 检查键是否存在
    if scores.contains_key("Blue") {
        println!("    Blue 队存在");
    }
    
    // 删除键值对
    let removed = scores.remove("Yellow");
    println!("    删除的值: {:?}", removed);
    println!("    删除后: {:?}", scores);
    
    // 长度
    println!("    映射大小: {}", scores.len());
}

fn hashmap_iteration() {
    println!("  哈希映射遍历:");
    
    let mut scores = HashMap::new();
    scores.insert(String::from("Blue"), 10);
    scores.insert(String::from("Yellow"), 50);
    scores.insert(String::from("Red"), 30);
    
    // 遍历键值对
    println!("    键值对:");
    for (key, value) in &scores {
        println!("      {}: {}", key, value);
    }
    
    // 只遍历键
    print!("    键: ");
    for key in scores.keys() {
        print!("{} ", key);
    }
    println!();
    
    // 只遍历值
    print!("    值: ");
    for value in scores.values() {
        print!("{} ", value);
    }
    println!();
    
    // 可变遍历值
    for value in scores.values_mut() {
        *value += 10;
    }
    println!("    增加10后: {:?}", scores);
}

fn hashmap_updates() {
    println!("  哈希映射更新:");
    
    let mut scores = HashMap::new();
    
    // 覆盖值
    scores.insert(String::from("Blue"), 10);
    scores.insert(String::from("Blue"), 25);
    println!("    覆盖后: {:?}", scores);
    
    // 只在键不存在时插入
    scores.entry(String::from("Yellow")).or_insert(50);
    scores.entry(String::from("Blue")).or_insert(50); // 不会插入
    println!("    条件插入后: {:?}", scores);
    
    // 根据旧值更新
    let text = "hello world wonderful world";
    let mut map = HashMap::new();
    
    for word in text.split_whitespace() {
        let count = map.entry(word).or_insert(0);
        *count += 1;
    }
    println!("    单词计数: {:?}", map);
}

// 4. 其他集合类型
fn other_collections() {
    println!("4. 其他集合类型:");
    
    // VecDeque - 双端队列
    use std::collections::VecDeque;
    let mut deque = VecDeque::new();
    deque.push_back(1);
    deque.push_back(2);
    deque.push_front(0);
    println!("  VecDeque: {:?}", deque);
    
    // HashSet - 哈希集合
    use std::collections::HashSet;
    let mut set = HashSet::new();
    set.insert("apple");
    set.insert("banana");
    set.insert("apple"); // 重复元素不会被添加
    println!("  HashSet: {:?}", set);
    
    // BTreeMap - 有序映射
    use std::collections::BTreeMap;
    let mut btree = BTreeMap::new();
    btree.insert(3, "three");
    btree.insert(1, "one");
    btree.insert(2, "two");
    println!("  BTreeMap: {:?}", btree);
    
    // BTreeSet - 有序集合
    use std::collections::BTreeSet;
    let mut btree_set = BTreeSet::new();
    btree_set.insert(3);
    btree_set.insert(1);
    btree_set.insert(2);
    println!("  BTreeSet: {:?}", btree_set);
    
    println!();
}

/*
关键要点总结：

1. Vec<T> - 动态数组：
   - 可变长度，元素类型相同
   - 提供 push、pop、insert、remove 等方法
   - 支持索引访问和迭代器

2. String - 字符串：
   - UTF-8 编码，可变长度
   - 不支持索引，支持切片（需注意字节边界）
   - 提供丰富的字符串操作方法

3. HashMap<K, V> - 哈希映射：
   - 键值对存储，键必须实现 Hash trait
   - 提供 O(1) 平均时间复杂度的访问
   - 支持条件插入和更新

4. 其他集合：
   - VecDeque：双端队列
   - HashSet：哈希集合
   - BTreeMap/BTreeSet：有序映射/集合

5. 选择建议：
   - 需要索引访问：Vec
   - 需要文本处理：String
   - 需要键值映射：HashMap
   - 需要唯一性：HashSet
   - 需要有序：BTree 系列

编译和运行：
cargo run --bin 09_collections
*/
