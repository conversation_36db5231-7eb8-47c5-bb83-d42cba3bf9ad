// 07_structs.rs
// Rust 结构体详解

fn main() {
    println!("=== Rust 结构体 ===\n");

    // 1. 结构体定义和实例化
    struct_definition_and_instantiation();
    
    // 2. 结构体方法
    struct_methods();
    
    // 3. 关联函数
    associated_functions();
    
    // 4. 元组结构体
    tuple_structs();
    
    // 5. 单元结构体
    unit_structs();
}

// 1. 结构体定义和实例化
fn struct_definition_and_instantiation() {
    println!("1. 结构体定义和实例化:");
    
    // 定义结构体
    struct User {
        active: bool,
        username: String,
        email: String,
        sign_in_count: u64,
    }
    
    // 创建结构体实例
    let user1 = User {
        email: String::from("<EMAIL>"),
        username: String::from("someusername123"),
        active: true,
        sign_in_count: 1,
    };
    
    println!("  用户1:");
    println!("    邮箱: {}", user1.email);
    println!("    用户名: {}", user1.username);
    println!("    活跃: {}", user1.active);
    println!("    登录次数: {}", user1.sign_in_count);
    
    // 可变结构体实例
    let mut user2 = User {
        email: String::from("<EMAIL>"),
        username: String::from("anotherusername456"),
        active: true,
        sign_in_count: 1,
    };
    
    // 修改字段
    user2.email = String::from("<EMAIL>");
    println!("  用户2 修改后的邮箱: {}", user2.email);
    
    // 使用函数创建实例
    let user3 = build_user(
        String::from("<EMAIL>"),
        String::from("user3"),
    );
    println!("  用户3 邮箱: {}", user3.email);
    
    // 结构体更新语法
    let user4 = User {
        email: String::from("<EMAIL>"),
        ..user3 // 使用 user3 的其他字段值
    };
    println!("  用户4 邮箱: {}", user4.email);
    println!("  用户4 用户名: {}", user4.username);
    
    // 注意：user3 的 username 被移动到 user4，user3 不再完全有效
    // println!("user3 用户名: {}", user3.username); // 编译错误
    
    println!();
}

fn build_user(email: String, username: String) -> User {
    User {
        email,    // 字段初始化简写
        username, // 字段初始化简写
        active: true,
        sign_in_count: 1,
    }
}

// 为了演示方法，重新定义 User 结构体
struct User {
    active: bool,
    username: String,
    email: String,
    sign_in_count: u64,
}

// 2. 结构体方法
fn struct_methods() {
    println!("2. 结构体方法:");
    
    // 定义一个矩形结构体
    #[derive(Debug)] // 允许打印调试信息
    struct Rectangle {
        width: u32,
        height: u32,
    }
    
    // 实现方法
    impl Rectangle {
        // 方法：第一个参数是 self
        fn area(&self) -> u32 {
            self.width * self.height
        }
        
        // 方法可以获取 self 的所有权
        fn max_dimension(self) -> u32 {
            if self.width > self.height {
                self.width
            } else {
                self.height
            }
        }
        
        // 方法可以可变借用 self
        fn double(&mut self) {
            self.width *= 2;
            self.height *= 2;
        }
        
        // 方法可以接受其他参数
        fn can_hold(&self, other: &Rectangle) -> bool {
            self.width > other.width && self.height > other.height
        }
    }
    
    let rect1 = Rectangle {
        width: 30,
        height: 50,
    };
    
    println!("  矩形: {:?}", rect1);
    println!("  面积: {}", rect1.area());
    
    let rect2 = Rectangle {
        width: 10,
        height: 40,
    };
    
    let rect3 = Rectangle {
        width: 60,
        height: 45,
    };
    
    println!("  rect1 能容纳 rect2: {}", rect1.can_hold(&rect2));
    println!("  rect1 能容纳 rect3: {}", rect1.can_hold(&rect3));
    
    // 使用可变方法
    let mut rect4 = Rectangle {
        width: 10,
        height: 20,
    };
    println!("  双倍前: {:?}", rect4);
    rect4.double();
    println!("  双倍后: {:?}", rect4);
    
    // 使用消费 self 的方法
    let rect5 = Rectangle {
        width: 15,
        height: 25,
    };
    let max_dim = rect5.max_dimension();
    println!("  最大维度: {}", max_dim);
    // rect5 现在不再有效
    
    println!();
}

// 3. 关联函数
fn associated_functions() {
    println!("3. 关联函数:");
    
    #[derive(Debug)]
    struct Rectangle {
        width: u32,
        height: u32,
    }
    
    impl Rectangle {
        // 关联函数：不以 self 作为第一个参数
        fn new(width: u32, height: u32) -> Rectangle {
            Rectangle { width, height }
        }
        
        // 创建正方形的关联函数
        fn square(size: u32) -> Rectangle {
            Rectangle {
                width: size,
                height: size,
            }
        }
        
        // 常量关联函数
        fn default() -> Rectangle {
            Rectangle {
                width: 1,
                height: 1,
            }
        }
    }
    
    // 使用关联函数（类似静态方法）
    let rect1 = Rectangle::new(10, 20);
    println!("  新矩形: {:?}", rect1);
    
    let square = Rectangle::square(15);
    println!("  正方形: {:?}", square);
    
    let default_rect = Rectangle::default();
    println!("  默认矩形: {:?}", default_rect);
    
    // 多个 impl 块
    impl Rectangle {
        fn perimeter(&self) -> u32 {
            2 * (self.width + self.height)
        }
    }
    
    println!("  矩形周长: {}", rect1.perimeter());
    
    println!();
}

// 4. 元组结构体
fn tuple_structs() {
    println!("4. 元组结构体:");
    
    // 定义元组结构体
    struct Color(i32, i32, i32);
    struct Point(i32, i32, i32);
    
    let black = Color(0, 0, 0);
    let origin = Point(0, 0, 0);
    
    println!("  黑色: ({}, {}, {})", black.0, black.1, black.2);
    println!("  原点: ({}, {}, {})", origin.0, origin.1, origin.2);
    
    // 元组结构体的解构
    let Color(r, g, b) = black;
    println!("  解构黑色: r={}, g={}, b={}", r, g, b);
    
    // 为元组结构体实现方法
    impl Color {
        fn new(r: i32, g: i32, b: i32) -> Color {
            Color(r, g, b)
        }
        
        fn red(&self) -> i32 {
            self.0
        }
        
        fn green(&self) -> i32 {
            self.1
        }
        
        fn blue(&self) -> i32 {
            self.2
        }
        
        fn brightness(&self) -> f64 {
            (self.0 as f64 + self.1 as f64 + self.2 as f64) / 3.0
        }
    }
    
    let red = Color::new(255, 0, 0);
    println!("  红色亮度: {:.2}", red.brightness());
    
    // 单字段元组结构体（新类型模式）
    struct Meters(f64);
    struct Kilometers(f64);
    
    let distance_m = Meters(1000.0);
    let distance_km = Kilometers(1.0);
    
    println!("  距离（米）: {}", distance_m.0);
    println!("  距离（千米）: {}", distance_km.0);
    
    println!();
}

// 5. 单元结构体
fn unit_structs() {
    println!("5. 单元结构体:");
    
    // 单元结构体：没有任何字段
    struct AlwaysEqual;
    
    let subject = AlwaysEqual;
    
    // 为单元结构体实现 trait
    impl PartialEq for AlwaysEqual {
        fn eq(&self, _other: &Self) -> bool {
            true
        }
    }
    
    let another = AlwaysEqual;
    println!("  单元结构体相等: {}", subject == another);
    
    // 单元结构体常用于实现 trait 而不需要存储数据
    struct FileLogger;
    struct ConsoleLogger;
    
    trait Logger {
        fn log(&self, message: &str);
    }
    
    impl Logger for FileLogger {
        fn log(&self, message: &str) {
            println!("  [文件] {}", message);
        }
    }
    
    impl Logger for ConsoleLogger {
        fn log(&self, message: &str) {
            println!("  [控制台] {}", message);
        }
    }
    
    let file_logger = FileLogger;
    let console_logger = ConsoleLogger;
    
    file_logger.log("这是文件日志");
    console_logger.log("这是控制台日志");
    
    println!();
}

// 结构体的高级特性
#[allow(dead_code)]
fn advanced_struct_features() {
    println!("结构体的高级特性:");
    
    // 泛型结构体
    #[derive(Debug)]
    struct Point<T> {
        x: T,
        y: T,
    }
    
    let integer_point = Point { x: 5, y: 10 };
    let float_point = Point { x: 1.0, y: 4.0 };
    
    println!("  整数点: {:?}", integer_point);
    println!("  浮点点: {:?}", float_point);
    
    // 多个泛型参数
    #[derive(Debug)]
    struct MixedPoint<T, U> {
        x: T,
        y: U,
    }
    
    let mixed = MixedPoint { x: 5, y: 4.0 };
    println!("  混合点: {:?}", mixed);
    
    // 带生命周期的结构体
    struct ImportantExcerpt<'a> {
        part: &'a str,
    }
    
    let novel = String::from("Call me Ishmael. Some years ago...");
    let first_sentence = novel.split('.').next().expect("Could not find a '.'");
    let excerpt = ImportantExcerpt {
        part: first_sentence,
    };
    
    println!("  重要摘录: {}", excerpt.part);
}

/*
关键要点总结：

1. 结构体定义：
   - 使用 struct 关键字
   - 字段必须指定类型
   - 支持字段初始化简写

2. 结构体实例：
   - 使用 {} 语法创建
   - 可以使用结构体更新语法 ..
   - 整个实例必须是可变的才能修改字段

3. 方法：
   - 在 impl 块中定义
   - 第一个参数是 self、&self 或 &mut self
   - 使用点号语法调用

4. 关联函数：
   - 不以 self 作为第一个参数
   - 使用 :: 语法调用
   - 常用于构造函数

5. 结构体类型：
   - 命名字段结构体
   - 元组结构体
   - 单元结构体

编译和运行：
cargo run --bin 07_structs
*/
