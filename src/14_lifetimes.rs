// 14_lifetimes.rs
// Rust 生命周期详解

fn main() {
    println!("=== Rust 生命周期 ===\n");

    // 1. 生命周期基础
    lifetime_basics();
    
    // 2. 函数中的生命周期
    lifetimes_in_functions();
    
    // 3. 结构体中的生命周期
    lifetimes_in_structs();
    
    // 4. 生命周期省略规则
    lifetime_elision_rules();
    
    // 5. 静态生命周期
    static_lifetimes();
    
    // 6. 高级生命周期特性
    advanced_lifetime_features();
}

// 1. 生命周期基础
fn lifetime_basics() {
    println!("1. 生命周期基础:");
    
    // 生命周期确保引用有效
    {
        let r;                // ---------+-- 'a
                             //          |
        {                    //          |
            let x = 5;       // -+-- 'b  |
            r = &x;          //  |       |
        }                    // -+       |
                             //          |
        // println!("r: {}", r); // 这会编译错误，因为 x 已经离开作用域
    }                        // ---------+
    
    // 正确的生命周期
    {
        let x = 5;            // ----------+-- 'b
        let r = &x;           // --+-- 'a  |
        println!("  r: {}", r); //   |       |
    }                         // --+-------+
    
    println!("  生命周期确保引用在被引用的值有效期内");
    
    println!();
}

// 2. 函数中的生命周期
fn lifetimes_in_functions() {
    println!("2. 函数中的生命周期:");
    
    // 需要生命周期注解的情况
    fn longest<'a>(x: &'a str, y: &'a str) -> &'a str {
        if x.len() > y.len() {
            x
        } else {
            y
        }
    }
    
    let string1 = String::from("abcd");
    let string2 = "xyz";
    
    let result = longest(string1.as_str(), string2);
    println!("  最长的字符串是 {}", result);
    
    // 生命周期注解的含义
    demonstrate_lifetime_meaning();
    
    // 不同生命周期的例子
    different_lifetimes_example();
    
    println!();
}

fn demonstrate_lifetime_meaning() {
    println!("  生命周期注解的含义:");
    
    fn longest<'a>(x: &'a str, y: &'a str) -> &'a str {
        if x.len() > y.len() {
            x
        } else {
            y
        }
    }
    
    let string1 = String::from("long string is long");
    
    {
        let string2 = String::from("xyz");
        let result = longest(string1.as_str(), string2.as_str());
        println!("    最长的字符串是 {}", result);
    }
    
    // 以下代码会编译错误，因为 string2 的生命周期太短
    // let string1 = String::from("long string is long");
    // let result;
    // {
    //     let string2 = String::from("xyz");
    //     result = longest(string1.as_str(), string2.as_str());
    // }
    // println!("最长的字符串是 {}", result);
}

fn different_lifetimes_example() {
    println!("  不同生命周期的例子:");
    
    // 返回值的生命周期不依赖于所有参数
    fn first_word<'a>(s: &'a str, _announcement: &str) -> &'a str {
        let bytes = s.as_bytes();
        for (i, &item) in bytes.iter().enumerate() {
            if item == b' ' {
                return &s[0..i];
            }
        }
        &s[..]
    }
    
    let sentence = String::from("hello world");
    let announcement = String::from("注意");
    let word = first_word(&sentence, &announcement);
    println!("    第一个单词: {}", word);
    
    // 返回值生命周期与输入无关的情况
    fn get_static_str() -> &'static str {
        "这是一个静态字符串"
    }
    
    println!("    静态字符串: {}", get_static_str());
}

// 3. 结构体中的生命周期
fn lifetimes_in_structs() {
    println!("3. 结构体中的生命周期:");
    
    // 包含引用的结构体需要生命周期注解
    struct ImportantExcerpt<'a> {
        part: &'a str,
    }
    
    let novel = String::from("Call me Ishmael. Some years ago...");
    let first_sentence = novel.split('.').next().expect("Could not find a '.'");
    let i = ImportantExcerpt {
        part: first_sentence,
    };
    
    println!("  重要摘录: {}", i.part);
    
    // 结构体方法中的生命周期
    impl<'a> ImportantExcerpt<'a> {
        // 方法的生命周期注解
        fn level(&self) -> i32 {
            3
        }
        
        // 返回引用的方法
        fn announce_and_return_part(&self, announcement: &str) -> &str {
            println!("    注意！{}", announcement);
            self.part
        }
        
        // 多个生命周期参数
        fn announce_and_return_part_with_lifetime<'b>(
            &self,
            announcement: &'b str,
        ) -> &'b str
        where
            'a: 'b, // 'a 比 'b 活得更久
        {
            println!("    重要通知！{}", announcement);
            announcement
        }
    }
    
    println!("  摘录级别: {}", i.level());
    let result = i.announce_and_return_part("这很重要");
    println!("  返回的部分: {}", result);
    
    println!();
}

// 4. 生命周期省略规则
fn lifetime_elision_rules() {
    println!("4. 生命周期省略规则:");
    
    // 规则1：每个引用参数都有自己的生命周期参数
    fn first_word(s: &str) -> &str {
        // 编译器推断为：fn first_word<'a>(s: &'a str) -> &'a str
        let bytes = s.as_bytes();
        for (i, &item) in bytes.iter().enumerate() {
            if item == b' ' {
                return &s[0..i];
            }
        }
        &s[..]
    }
    
    // 规则2：如果只有一个输入生命周期参数，它被赋予所有输出生命周期参数
    fn get_first_char(s: &str) -> &str {
        // 编译器推断为：fn get_first_char<'a>(s: &'a str) -> &'a str
        &s[0..1]
    }
    
    // 规则3：如果有多个输入生命周期参数，但其中一个是 &self 或 &mut self，
    // 那么 self 的生命周期被赋予所有输出生命周期参数
    
    struct StrWrapper<'a> {
        s: &'a str,
    }
    
    impl<'a> StrWrapper<'a> {
        fn get_str(&self) -> &str {
            // 编译器推断为：fn get_str<'a>(&'a self) -> &'a str
            self.s
        }
    }
    
    let text = "hello world";
    let word = first_word(text);
    println!("  第一个单词: {}", word);
    
    let first_char = get_first_char(text);
    println!("  第一个字符: {}", first_char);
    
    let wrapper = StrWrapper { s: text };
    println!("  包装的字符串: {}", wrapper.get_str());
    
    println!();
}

// 5. 静态生命周期
fn static_lifetimes() {
    println!("5. 静态生命周期:");
    
    // 'static 生命周期表示整个程序运行期间
    let s: &'static str = "我有静态生命周期";
    println!("  静态字符串: {}", s);
    
    // 字符串字面量都有 'static 生命周期
    fn get_static_string() -> &'static str {
        "这是静态的"
    }
    
    println!("  静态函数返回: {}", get_static_string());
    
    // 静态变量
    static HELLO_WORLD: &str = "Hello, world!";
    println!("  静态变量: {}", HELLO_WORLD);
    
    // 注意：不要随意使用 'static
    // 大多数情况下，问题在于尝试创建悬垂引用或生命周期不匹配
    
    println!();
}

// 6. 高级生命周期特性
fn advanced_lifetime_features() {
    println!("6. 高级生命周期特性:");
    
    // 生命周期子类型
    lifetime_subtyping();
    
    // 生命周期约束
    lifetime_bounds();
    
    // 高阶 trait 约束
    higher_ranked_trait_bounds();
    
    println!();
}

fn lifetime_subtyping() {
    println!("  生命周期子类型:");
    
    // 较长的生命周期是较短生命周期的子类型
    fn choose_first<'a: 'b, 'b>(first: &'a str, _second: &'b str) -> &'b str {
        first // 'a: 'b 意味着 'a 至少和 'b 一样长
    }
    
    let string1 = String::from("long string is long");
    let result;
    {
        let string2 = String::from("short");
        result = choose_first(&string1, &string2);
    }
    println!("    选择的字符串: {}", result);
}

fn lifetime_bounds() {
    println!("  生命周期约束:");
    
    use std::fmt::Display;
    
    // 泛型类型参数、trait 约束和生命周期的结合
    fn longest_with_an_announcement<'a, T>(
        x: &'a str,
        y: &'a str,
        ann: T,
    ) -> &'a str
    where
        T: Display,
    {
        println!("    公告！{}", ann);
        if x.len() > y.len() {
            x
        } else {
            y
        }
    }
    
    let string1 = String::from("abcd");
    let string2 = "xyz";
    let announcement = "今天是特殊的日子";
    
    let result = longest_with_an_announcement(
        string1.as_str(),
        string2,
        announcement,
    );
    println!("    最长的字符串是 {}", result);
}

fn higher_ranked_trait_bounds() {
    println!("  高阶 trait 约束:");
    
    // for<'a> 语法表示对于任何生命周期 'a
    fn apply_to_all<F>(f: F) 
    where
        F: for<'a> Fn(&'a str) -> &'a str,
    {
        let s1 = "hello";
        let s2 = "world";
        println!("    应用到 '{}': {}", s1, f(s1));
        println!("    应用到 '{}': {}", s2, f(s2));
    }
    
    fn identity(s: &str) -> &str {
        s
    }
    
    apply_to_all(identity);
    
    // 闭包的高阶 trait 约束
    apply_to_all(|s| s);
}

// 生命周期的实际应用示例
#[allow(dead_code)]
fn practical_lifetime_examples() {
    println!("生命周期的实际应用:");
    
    // 解析器示例
    struct Parser<'a> {
        input: &'a str,
        position: usize,
    }
    
    impl<'a> Parser<'a> {
        fn new(input: &'a str) -> Self {
            Parser { input, position: 0 }
        }
        
        fn parse_word(&mut self) -> Option<&'a str> {
            let start = self.position;
            while self.position < self.input.len() {
                if self.input.chars().nth(self.position) == Some(' ') {
                    break;
                }
                self.position += 1;
            }
            
            if start == self.position {
                None
            } else {
                let word = &self.input[start..self.position];
                self.position += 1; // 跳过空格
                Some(word)
            }
        }
    }
    
    let text = "hello world rust";
    let mut parser = Parser::new(text);
    
    while let Some(word) = parser.parse_word() {
        println!("  解析的单词: {}", word);
    }
    
    // 缓存示例
    use std::collections::HashMap;
    
    struct Cache<'a> {
        data: HashMap<&'a str, i32>,
    }
    
    impl<'a> Cache<'a> {
        fn new() -> Self {
            Cache {
                data: HashMap::new(),
            }
        }
        
        fn get(&self, key: &str) -> Option<&i32> {
            self.data.get(key)
        }
        
        fn insert(&mut self, key: &'a str, value: i32) {
            self.data.insert(key, value);
        }
    }
    
    let key1 = "first";
    let key2 = "second";
    
    let mut cache = Cache::new();
    cache.insert(key1, 100);
    cache.insert(key2, 200);
    
    if let Some(value) = cache.get("first") {
        println!("  缓存值: {}", value);
    }
}

/*
关键要点总结：

1. 生命周期基础：
   - 确保引用有效
   - 防止悬垂引用
   - 编译时检查

2. 生命周期注解：
   - 使用 'a, 'b 等标记
   - 描述引用之间的关系
   - 不改变生命周期，只是描述

3. 省略规则：
   - 单个输入参数：输出继承其生命周期
   - 多个输入参数：需要显式注解
   - 方法中的 self：输出继承 self 的生命周期

4. 结构体生命周期：
   - 包含引用的结构体需要生命周期注解
   - 实例不能比其引用的数据活得更久

5. 静态生命周期：
   - 'static 表示整个程序期间
   - 字符串字面量默认是 'static
   - 谨慎使用

6. 高级特性：
   - 生命周期子类型
   - 生命周期约束
   - 高阶 trait 约束

编译和运行：
cargo run --bin 14_lifetimes
*/
