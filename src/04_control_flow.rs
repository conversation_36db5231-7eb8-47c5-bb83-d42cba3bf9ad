// 04_control_flow.rs
// Rust 流程控制详解

fn main() {
    println!("=== Rust 流程控制 ===\n");

    // 1. if 表达式
    if_expressions();
    
    // 2. 循环
    loops();
    
    // 3. match 表达式
    match_expressions();
    
    // 4. if let 和 while let
    if_let_and_while_let();
}

// 1. if 表达式
fn if_expressions() {
    println!("1. if 表达式:");
    
    // 基本 if
    let number = 6;
    if number % 4 == 0 {
        println!("  {} 能被 4 整除", number);
    } else if number % 3 == 0 {
        println!("  {} 能被 3 整除", number);
    } else if number % 2 == 0 {
        println!("  {} 能被 2 整除", number);
    } else {
        println!("  {} 不能被 4、3、2 整除", number);
    }
    
    // if 作为表达式
    let condition = true;
    let number = if condition { 5 } else { 6 };
    println!("  条件表达式的值: {}", number);
    
    // 复杂条件
    let x = 10;
    let y = 20;
    if x > 5 && y < 30 {
        println!("  复合条件为真");
    }
    
    // 嵌套 if
    let outer_condition = true;
    if outer_condition {
        let inner_condition = false;
        if inner_condition {
            println!("  内层条件为真");
        } else {
            println!("  外层为真，内层为假");
        }
    }
    
    println!();
}

// 2. 循环
fn loops() {
    println!("2. 循环:");
    
    // loop 循环
    loop_example();
    
    // while 循环
    while_example();
    
    // for 循环
    for_example();
    
    // 循环标签和控制
    loop_labels_and_control();
    
    println!();
}

fn loop_example() {
    println!("  loop 循环:");
    
    let mut counter = 0;
    let result = loop {
        counter += 1;
        
        if counter == 3 {
            println!("    计数器达到 3，继续...");
            continue; // 跳过本次迭代
        }
        
        if counter == 5 {
            break counter * 2; // 退出循环并返回值
        }
        
        println!("    计数器: {}", counter);
    };
    
    println!("    loop 返回值: {}", result);
}

fn while_example() {
    println!("  while 循环:");
    
    let mut number = 3;
    while number != 0 {
        println!("    倒计时: {}", number);
        number -= 1;
    }
    println!("    发射!");
    
    // while 条件可以很复杂
    let mut x = 1;
    let mut y = 1;
    while x < 10 && y < 20 {
        println!("    x: {}, y: {}", x, y);
        x += 1;
        y += 2;
    }
}

fn for_example() {
    println!("  for 循环:");
    
    // 遍历数组
    let array = [10, 20, 30, 40, 50];
    for element in array {
        println!("    数组元素: {}", element);
    }
    
    // 遍历范围
    for number in 1..4 {
        println!("    范围 1..4: {}", number);
    }
    
    // 包含结束值的范围
    for number in 1..=3 {
        println!("    范围 1..=3: {}", number);
    }
    
    // 遍历集合
    let vec = vec!["a", "b", "c"];
    for (index, value) in vec.iter().enumerate() {
        println!("    索引 {}: {}", index, value);
    }
    
    // 反向遍历
    for number in (1..4).rev() {
        println!("    反向: {}", number);
    }
}

fn loop_labels_and_control() {
    println!("  循环标签和控制:");
    
    let mut count = 0;
    'counting_up: loop {
        println!("    外层循环 count = {}", count);
        let mut remaining = 10;
        
        loop {
            println!("      内层循环 remaining = {}", remaining);
            if remaining == 9 {
                break; // 只退出内层循环
            }
            if count == 2 {
                break 'counting_up; // 退出外层循环
            }
            remaining -= 1;
        }
        
        count += 1;
    }
    println!("    结束计数，count = {}", count);
}

// 3. match 表达式
fn match_expressions() {
    println!("3. match 表达式:");
    
    // 基本 match
    let number = 3;
    match number {
        1 => println!("  一"),
        2 => println!("  二"),
        3 => println!("  三"),
        4 | 5 => println!("  四或五"),
        6..=10 => println!("  六到十"),
        _ => println!("  其他"),
    }
    
    // match 作为表达式
    let number = 6;
    let description = match number {
        1 => "一",
        2 => "二",
        3 => "三",
        _ => "其他",
    };
    println!("  描述: {}", description);
    
    // 匹配元组
    let point = (0, 1);
    match point {
        (0, 0) => println!("  原点"),
        (0, y) => println!("  在 Y 轴上，y = {}", y),
        (x, 0) => println!("  在 X 轴上，x = {}", x),
        (x, y) => println!("  点 ({}, {})", x, y),
    }
    
    // 匹配 Option
    let some_number = Some(5);
    match some_number {
        Some(x) if x < 5 => println!("  小于 5: {}", x),
        Some(x) => println!("  大于等于 5: {}", x),
        None => println!("  没有值"),
    }
    
    // 匹配结构体
    struct Point {
        x: i32,
        y: i32,
    }
    
    let p = Point { x: 0, y: 7 };
    match p {
        Point { x, y: 0 } => println!("  在 X 轴上，x = {}", x),
        Point { x: 0, y } => println!("  在 Y 轴上，y = {}", y),
        Point { x, y } => println!("  点 ({}, {})", x, y),
    }
    
    println!();
}

// 4. if let 和 while let
fn if_let_and_while_let() {
    println!("4. if let 和 while let:");
    
    // if let 简化 match
    let some_value = Some(3);
    
    // 使用 match
    match some_value {
        Some(3) => println!("  match: 值是 3"),
        _ => (),
    }
    
    // 使用 if let (更简洁)
    if let Some(3) = some_value {
        println!("  if let: 值是 3");
    }
    
    // if let 与 else
    let number = Some(7);
    if let Some(x) = number {
        println!("  if let: 值是 {}", x);
    } else {
        println!("  if let: 没有值");
    }
    
    // while let 循环
    let mut stack = Vec::new();
    stack.push(1);
    stack.push(2);
    stack.push(3);
    
    println!("  while let 弹出栈:");
    while let Some(top) = stack.pop() {
        println!("    弹出: {}", top);
    }
    
    // 复杂的 while let
    let mut optional = Some(0);
    while let Some(i) = optional {
        if i > 3 {
            println!("    退出循环");
            optional = None;
        } else {
            println!("    i 是 {}", i);
            optional = Some(i + 1);
        }
    }
    
    println!();
}

// 额外示例：模式匹配的高级用法
#[allow(dead_code)]
fn advanced_pattern_matching() {
    println!("高级模式匹配:");
    
    // 解构枚举
    enum Message {
        Quit,
        Move { x: i32, y: i32 },
        Write(String),
        ChangeColor(i32, i32, i32),
    }
    
    let msg = Message::ChangeColor(0, 160, 255);
    
    match msg {
        Message::Quit => println!("  退出"),
        Message::Move { x, y } => println!("  移动到 ({}, {})", x, y),
        Message::Write(text) => println!("  写入: {}", text),
        Message::ChangeColor(r, g, b) => println!("  改变颜色到 RGB({}, {}, {})", r, g, b),
    }
    
    // 守卫 (guard)
    let num = Some(4);
    match num {
        Some(x) if x < 5 => println!("  小于 5: {}", x),
        Some(x) => println!("  大于等于 5: {}", x),
        None => println!("  没有值"),
    }
    
    // @ 绑定
    let msg = Message::Move { x: 10, y: 20 };
    match msg {
        Message::Move { x: x_val @ 10..=20, y } => {
            println!("  x 在 10-20 范围内: x={}, y={}", x_val, y);
        }
        Message::Move { x, y } => {
            println!("  移动到其他位置: ({}, {})", x, y);
        }
        _ => (),
    }
}

/*
关键要点总结：

1. if 表达式：
   - 条件必须是 bool 类型
   - 可以作为表达式返回值
   - 支持 else if 和嵌套

2. 循环：
   - loop: 无限循环，可以返回值
   - while: 条件循环
   - for: 迭代循环，最常用
   - 支持 break 和 continue
   - 可以使用标签控制嵌套循环

3. match 表达式：
   - 必须穷尽所有可能
   - 支持模式匹配
   - 可以作为表达式返回值
   - 支持守卫条件

4. if let 和 while let：
   - 简化单一模式匹配
   - 更简洁的语法

编译和运行：
cargo run --bin 04_control_flow
*/
