// 05_ownership.rs
// Rust 所有权详解

fn main() {
    println!("=== Rust 所有权 ===\n");

    // 1. 所有权基础
    ownership_basics();
    
    // 2. 移动语义
    move_semantics();
    
    // 3. 克隆
    cloning();
    
    // 4. 函数与所有权
    functions_and_ownership();
    
    // 5. 栈与堆
    stack_and_heap();
}

// 1. 所有权基础
fn ownership_basics() {
    println!("1. 所有权基础:");
    
    // 所有权规则：
    // 1. Rust 中的每一个值都有一个被称为其所有者（owner）的变量
    // 2. 值在任一时刻有且只有一个所有者
    // 3. 当所有者（变量）离开作用域，这个值将被丢弃
    
    {
        let s = String::from("hello"); // s 进入作用域
        println!("  字符串: {}", s);
    } // s 离开作用域，内存被释放
    
    // s 在这里不再有效
    // println!("{}", s); // 这会导致编译错误
    
    println!("  作用域演示完成");
    println!();
}

// 2. 移动语义
fn move_semantics() {
    println!("2. 移动语义:");
    
    // 简单类型的复制
    let x = 5;
    let y = x; // 复制，因为 i32 实现了 Copy trait
    println!("  x = {}, y = {} (复制)", x, y);
    
    // 复杂类型的移动
    let s1 = String::from("hello");
    let s2 = s1; // 移动，s1 不再有效
    
    println!("  s2 = {} (移动)", s2);
    // println!("s1 = {}", s1); // 这会导致编译错误，因为 s1 已被移动
    
    // 移动发生在赋值时
    demonstrate_move_in_assignment();
    
    // 移动发生在函数调用时
    demonstrate_move_in_function_call();
    
    println!();
}

fn demonstrate_move_in_assignment() {
    println!("  赋值中的移动:");
    
    let s1 = String::from("world");
    println!("    s1 创建: {}", s1);
    
    let s2 = s1; // s1 被移动到 s2
    println!("    s1 移动到 s2: {}", s2);
    
    // s1 现在无效
    // println!("s1: {}", s1); // 编译错误
}

fn demonstrate_move_in_function_call() {
    println!("  函数调用中的移动:");
    
    let s = String::from("function");
    println!("    调用前: {}", s);
    
    takes_ownership(s); // s 被移动到函数中
    
    // s 现在无效
    // println!("调用后: {}", s); // 编译错误
}

fn takes_ownership(some_string: String) {
    println!("    函数内部: {}", some_string);
} // some_string 离开作用域并被丢弃

// 3. 克隆
fn cloning() {
    println!("3. 克隆:");
    
    // 使用 clone 方法进行深拷贝
    let s1 = String::from("hello");
    let s2 = s1.clone(); // 深拷贝
    
    println!("  s1 = {}, s2 = {} (克隆)", s1, s2);
    
    // 实现了 Copy trait 的类型
    demonstrate_copy_types();
    
    println!();
}

fn demonstrate_copy_types() {
    println!("  Copy 类型:");
    
    // 这些类型实现了 Copy trait，会自动复制而不是移动
    let x = 5;
    let y = x;
    println!("    整数: x = {}, y = {}", x, y);
    
    let a = true;
    let b = a;
    println!("    布尔: a = {}, b = {}", a, b);
    
    let c = 'A';
    let d = c;
    println!("    字符: c = {}, d = {}", c, d);
    
    let tuple1 = (1, 2);
    let tuple2 = tuple1;
    println!("    元组: tuple1 = {:?}, tuple2 = {:?}", tuple1, tuple2);
    
    // 数组也实现了 Copy（如果元素类型实现了 Copy）
    let arr1 = [1, 2, 3];
    let arr2 = arr1;
    println!("    数组: arr1 = {:?}, arr2 = {:?}", arr1, arr2);
}

// 4. 函数与所有权
fn functions_and_ownership() {
    println!("4. 函数与所有权:");
    
    // 传递所有权给函数
    let s = String::from("hello");
    let len = calculate_length_take_ownership(s);
    println!("  长度: {}", len);
    // s 已经被移动，不能再使用
    
    // 函数返回所有权
    let s1 = gives_ownership();
    println!("  获得所有权: {}", s1);
    
    // 传递并返回所有权
    let s2 = String::from("world");
    let s3 = takes_and_gives_back(s2);
    println!("  传递并返回: {}", s3);
    // s2 已被移动，s3 现在拥有所有权
    
    // 使用元组返回多个值
    let s4 = String::from("rust");
    let (s5, len) = calculate_length_with_tuple(s4);
    println!("  字符串: {}, 长度: {}", s5, len);
    
    println!();
}

fn calculate_length_take_ownership(s: String) -> usize {
    s.len()
} // s 离开作用域并被丢弃

fn gives_ownership() -> String {
    let some_string = String::from("yours");
    some_string // 返回所有权
}

fn takes_and_gives_back(a_string: String) -> String {
    a_string // 返回所有权
}

fn calculate_length_with_tuple(s: String) -> (String, usize) {
    let length = s.len();
    (s, length) // 返回字符串和长度
}

// 5. 栈与堆
fn stack_and_heap() {
    println!("5. 栈与堆:");
    
    // 栈上的数据
    stack_data();
    
    // 堆上的数据
    heap_data();
    
    // 混合示例
    mixed_example();
    
    println!();
}

fn stack_data() {
    println!("  栈上的数据:");
    
    // 这些数据存储在栈上，大小在编译时已知
    let x = 5;              // i32, 4 字节
    let y = true;           // bool, 1 字节
    let z = 'A';            // char, 4 字节
    let arr = [1, 2, 3];    // [i32; 3], 12 字节
    
    println!("    x: {}, y: {}, z: {}, arr: {:?}", x, y, z, arr);
    
    // 栈数据的复制很快
    let x2 = x; // 简单的位复制
    println!("    复制后: x: {}, x2: {}", x, x2);
}

fn heap_data() {
    println!("  堆上的数据:");
    
    // String 的数据存储在堆上，大小在运行时确定
    let mut s = String::from("hello");
    println!("    初始字符串: {}", s);
    
    // 可以动态增长
    s.push_str(", world!");
    println!("    增长后: {}", s);
    
    // Vec 也存储在堆上
    let mut vec = Vec::new();
    vec.push(1);
    vec.push(2);
    vec.push(3);
    println!("    动态数组: {:?}", vec);
}

fn mixed_example() {
    println!("  混合示例:");
    
    // 结构体可能同时包含栈和堆数据
    struct Person {
        name: String,    // 堆上
        age: u32,        // 栈上
        active: bool,    // 栈上
    }
    
    let person = Person {
        name: String::from("Alice"),
        age: 30,
        active: true,
    };
    
    println!("    姓名: {}, 年龄: {}, 活跃: {}", 
             person.name, person.age, person.active);
    
    // 移动整个结构体
    let person2 = person;
    println!("    移动后: {}", person2.name);
    // person 现在无效
}

// 所有权的好处演示
#[allow(dead_code)]
fn ownership_benefits() {
    println!("所有权的好处:");
    
    // 1. 内存安全：防止悬垂指针
    // 2. 无需垃圾回收：确定性的内存管理
    // 3. 线程安全：防止数据竞争
    // 4. 零成本抽象：编译时检查，运行时无开销
    
    println!("  - 防止内存泄漏");
    println!("  - 防止悬垂指针");
    println!("  - 防止缓冲区溢出");
    println!("  - 确保线程安全");
}

/*
关键要点总结：

1. 所有权规则：
   - 每个值都有一个所有者
   - 同时只能有一个所有者
   - 所有者离开作用域时，值被丢弃

2. 移动语义：
   - 复杂类型（如 String）赋值时会移动所有权
   - 移动后原变量不再有效
   - 简单类型（如 i32）会复制而不是移动

3. Copy vs Move：
   - 实现 Copy trait 的类型会复制
   - 未实现 Copy trait 的类型会移动

4. 函数调用：
   - 传递参数可能转移所有权
   - 返回值可以转移所有权

5. 内存管理：
   - 栈：编译时大小已知，快速分配/释放
   - 堆：运行时大小，较慢但灵活

编译和运行：
cargo run --bin 05_ownership
*/
