// 06_references_and_borrowing.rs
// Rust 引用和借用详解

fn main() {
    println!("=== Rust 引用和借用 ===\n");

    // 1. 引用基础
    reference_basics();
    
    // 2. 可变引用
    mutable_references();
    
    // 3. 借用规则
    borrowing_rules();
    
    // 4. 悬垂引用
    dangling_references();
    
    // 5. 切片
    slices();
}

// 1. 引用基础
fn reference_basics() {
    println!("1. 引用基础:");
    
    let s1 = String::from("hello");
    
    // 创建引用，不获取所有权
    let len = calculate_length(&s1);
    
    println!("  字符串 '{}' 的长度是 {}", s1, len);
    // s1 仍然有效，因为我们只是借用了它
    
    // 引用的语法
    let x = 5;
    let y = &x; // y 是 x 的引用
    
    println!("  x = {}", x);
    println!("  y = {} (引用)", y);
    println!("  *y = {} (解引用)", *y);
    
    // 引用的引用
    let z = &y;
    println!("  z = {} (引用的引用)", z);
    println!("  **z = {} (双重解引用)", **z);
    
    println!();
}

fn calculate_length(s: &String) -> usize {
    s.len()
} // s 离开作用域，但因为它不拥有引用值的所有权，所以什么也不会发生

// 2. 可变引用
fn mutable_references() {
    println!("2. 可变引用:");
    
    let mut s = String::from("hello");
    
    // 创建可变引用
    change(&mut s);
    
    println!("  修改后的字符串: {}", s);
    
    // 可变引用的限制演示
    demonstrate_mutable_reference_restrictions();
    
    println!();
}

fn change(some_string: &mut String) {
    some_string.push_str(", world");
}

fn demonstrate_mutable_reference_restrictions() {
    println!("  可变引用的限制:");
    
    let mut s = String::from("hello");
    
    {
        let r1 = &mut s; // 第一个可变引用
        println!("    r1: {}", r1);
    } // r1 离开作用域，现在可以创建新的引用
    
    let r2 = &mut s; // 第二个可变引用
    println!("    r2: {}", r2);
    
    // 以下代码会导致编译错误：不能同时有两个可变引用
    // let r3 = &mut s;
    // println!("{}, {}", r2, r3);
}

// 3. 借用规则
fn borrowing_rules() {
    println!("3. 借用规则:");
    
    // 规则1：在任意给定时间，要么只能有一个可变引用，要么只能有多个不可变引用
    rule_one_demo();
    
    // 规则2：引用必须总是有效的
    rule_two_demo();
    
    // 非词法作用域生命周期 (NLL)
    non_lexical_lifetimes();
    
    println!();
}

fn rule_one_demo() {
    println!("  规则1：可变引用的排他性");
    
    let mut s = String::from("hello");
    
    // 多个不可变引用是允许的
    let r1 = &s;
    let r2 = &s;
    println!("    不可变引用: {} 和 {}", r1, r2);
    
    // 不可变引用使用完毕后，可以创建可变引用
    let r3 = &mut s;
    println!("    可变引用: {}", r3);
    
    // 以下代码会编译错误：不能同时有可变和不可变引用
    // let r4 = &s;
    // println!("{}, {}", r3, r4);
}

fn rule_two_demo() {
    println!("  规则2：引用必须有效");
    
    let reference_to_nothing = no_dangle();
    println!("    有效引用: {}", reference_to_nothing);
}

fn no_dangle() -> String {
    let s = String::from("hello");
    s // 返回所有权而不是引用
}

// 以下函数会导致编译错误：返回悬垂引用
// fn dangle() -> &String {
//     let s = String::from("hello");
//     &s // 返回引用，但 s 即将被销毁
// }

fn non_lexical_lifetimes() {
    println!("  非词法作用域生命周期 (NLL):");
    
    let mut s = String::from("hello");
    
    let r1 = &s;
    let r2 = &s;
    println!("    r1: {}, r2: {}", r1, r2);
    // r1 和 r2 在这里不再使用
    
    let r3 = &mut s; // 这是允许的，因为 r1 和 r2 不再使用
    println!("    r3: {}", r3);
}

// 4. 悬垂引用
fn dangling_references() {
    println!("4. 悬垂引用:");
    
    // Rust 编译器防止悬垂引用
    println!("  Rust 编译器防止悬垂引用的产生");
    
    // 正确的做法：返回所有权
    let string = create_string();
    println!("  创建的字符串: {}", string);
    
    // 使用引用的正确方式
    let s = String::from("world");
    let len = get_length(&s);
    println!("  字符串 '{}' 长度: {}", s, len);
    
    println!();
}

fn create_string() -> String {
    String::from("created")
}

fn get_length(s: &String) -> usize {
    s.len()
}

// 5. 切片
fn slices() {
    println!("5. 切片:");
    
    // 字符串切片
    string_slices();
    
    // 数组切片
    array_slices();
    
    // 切片作为参数
    slice_parameters();
    
    println!();
}

fn string_slices() {
    println!("  字符串切片:");
    
    let s = String::from("hello world");
    
    let hello = &s[0..5];   // 或 &s[..5]
    let world = &s[6..11];  // 或 &s[6..]
    let whole = &s[..];     // 整个字符串
    
    println!("    原字符串: {}", s);
    println!("    hello: {}", hello);
    println!("    world: {}", world);
    println!("    whole: {}", whole);
    
    // 字符串字面量就是切片
    let literal = "Hello, world!"; // 类型是 &str
    println!("    字面量: {}", literal);
    
    // 使用切片的函数
    let word = first_word(&s);
    println!("    第一个单词: {}", word);
}

fn first_word(s: &str) -> &str {
    let bytes = s.as_bytes();
    
    for (i, &item) in bytes.iter().enumerate() {
        if item == b' ' {
            return &s[0..i];
        }
    }
    
    &s[..]
}

fn array_slices() {
    println!("  数组切片:");
    
    let a = [1, 2, 3, 4, 5];
    
    let slice = &a[1..4];
    println!("    原数组: {:?}", a);
    println!("    切片 [1..4]: {:?}", slice);
    
    // 切片的类型是 &[i32]
    assert_eq!(slice, &[2, 3, 4]);
    println!("    切片断言通过");
}

fn slice_parameters() {
    println!("  切片作为参数:");
    
    let s = String::from("hello world");
    let literal = "hello world";
    let array = [1, 2, 3, 4, 5];
    
    // 函数接受 &str 可以处理 String 和字符串字面量
    println!("    处理 String: {}", process_string(&s));
    println!("    处理字面量: {}", process_string(literal));
    
    // 函数接受切片可以处理数组和向量
    println!("    处理数组: {}", sum_slice(&array));
    
    let vec = vec![1, 2, 3];
    println!("    处理向量: {}", sum_slice(&vec));
}

fn process_string(s: &str) -> usize {
    s.len()
}

fn sum_slice(slice: &[i32]) -> i32 {
    slice.iter().sum()
}

// 引用和借用的高级示例
#[allow(dead_code)]
fn advanced_borrowing_examples() {
    println!("高级借用示例:");
    
    // 结构体中的引用
    struct ImportantExcerpt<'a> {
        part: &'a str,
    }
    
    let novel = String::from("Call me Ishmael. Some years ago...");
    let first_sentence = novel.split('.').next().expect("Could not find a '.'");
    let excerpt = ImportantExcerpt {
        part: first_sentence,
    };
    
    println!("  重要摘录: {}", excerpt.part);
    
    // 方法中的借用
    impl<'a> ImportantExcerpt<'a> {
        fn level(&self) -> i32 {
            3
        }
        
        fn announce_and_return_part(&self, announcement: &str) -> &str {
            println!("Attention please: {}", announcement);
            self.part
        }
    }
    
    println!("  级别: {}", excerpt.level());
    let result = excerpt.announce_and_return_part("重要消息");
    println!("  返回部分: {}", result);
}

/*
关键要点总结：

1. 引用基础：
   - 使用 & 创建引用，不获取所有权
   - 使用 * 解引用
   - 引用允许使用值但不拥有它

2. 可变引用：
   - 使用 &mut 创建可变引用
   - 可以通过可变引用修改值

3. 借用规则：
   - 同时只能有一个可变引用，或多个不可变引用
   - 引用必须总是有效的
   - 编译器防止悬垂引用

4. 切片：
   - 字符串切片：&str
   - 数组切片：&[T]
   - 不拥有所有权的引用

5. 好处：
   - 内存安全
   - 无需手动内存管理
   - 编译时检查

编译和运行：
cargo run --bin 06_references_and_borrowing
*/
