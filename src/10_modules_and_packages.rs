// 10_modules_and_packages.rs
// Rust 模块和包详解

fn main() {
    println!("=== Rust 模块和包 ===\n");

    // 1. 模块基础
    module_basics();
    
    // 2. 模块路径
    module_paths();
    
    // 3. use 关键字
    use_keyword();
    
    // 4. 公有性和私有性
    privacy_demo();
    
    // 5. 文件模块
    file_modules_demo();
}

// 1. 模块基础
fn module_basics() {
    println!("1. 模块基础:");
    
    // 在函数内定义模块（仅用于演示）
    mod front_of_house {
        pub mod hosting {
            pub fn add_to_waitlist() {
                println!("    添加到等待列表");
            }
            
            fn seat_at_table() {
                println!("    安排座位");
            }
        }
        
        mod serving {
            fn take_order() {
                println!("    接受订单");
            }
            
            fn serve_order() {
                println!("    提供订单");
            }
            
            fn take_payment() {
                println!("    收取付款");
            }
        }
    }
    
    // 调用公有函数
    front_of_house::hosting::add_to_waitlist();
    
    // 以下调用会编译错误，因为函数是私有的
    // front_of_house::hosting::seat_at_table();
    // front_of_house::serving::take_order();
    
    println!();
}

// 2. 模块路径
fn module_paths() {
    println!("2. 模块路径:");
    
    mod restaurant {
        pub mod front_of_house {
            pub mod hosting {
                pub fn add_to_waitlist() {
                    println!("    前台：添加到等待列表");
                }
            }
        }
        
        pub fn eat_at_restaurant() {
            // 绝对路径
            crate::module_paths::restaurant::front_of_house::hosting::add_to_waitlist();
            
            // 相对路径
            front_of_house::hosting::add_to_waitlist();
        }
        
        mod back_of_house {
            pub struct Breakfast {
                pub toast: String,
                seasonal_fruit: String,
            }
            
            impl Breakfast {
                pub fn summer(toast: &str) -> Breakfast {
                    Breakfast {
                        toast: String::from(toast),
                        seasonal_fruit: String::from("peaches"),
                    }
                }
            }
            
            pub enum Appetizer {
                Soup,
                Salad,
            }
        }
        
        pub fn eat_breakfast() {
            // 使用结构体
            let mut meal = back_of_house::Breakfast::summer("Rye");
            meal.toast = String::from("Wheat");
            println!("    我想要 {} 吐司", meal.toast);
            
            // 以下代码会编译错误，因为 seasonal_fruit 是私有的
            // meal.seasonal_fruit = String::from("blueberries");
            
            // 使用枚举
            let order1 = back_of_house::Appetizer::Soup;
            let order2 = back_of_house::Appetizer::Salad;
            
            match order1 {
                back_of_house::Appetizer::Soup => println!("    点了汤"),
                back_of_house::Appetizer::Salad => println!("    点了沙拉"),
            }
        }
    }
    
    restaurant::eat_at_restaurant();
    restaurant::eat_breakfast();
    
    println!();
}

// 3. use 关键字
fn use_keyword() {
    println!("3. use 关键字:");
    
    mod front_of_house {
        pub mod hosting {
            pub fn add_to_waitlist() {
                println!("    使用 use 添加到等待列表");
            }
        }
    }
    
    // 使用 use 引入路径
    use front_of_house::hosting;
    
    hosting::add_to_waitlist();
    
    // 使用 use 引入函数（不推荐）
    use front_of_house::hosting::add_to_waitlist;
    add_to_waitlist();
    
    // 使用 as 关键字重命名
    use front_of_house::hosting::add_to_waitlist as add_guest;
    add_guest();
    
    // 重新导出
    pub use front_of_house::hosting as public_hosting;
    
    // 使用嵌套路径
    use std::collections::{HashMap, BTreeMap};
    let mut map1 = HashMap::new();
    let mut map2 = BTreeMap::new();
    map1.insert("key", "value");
    map2.insert("key", "value");
    println!("    HashMap: {:?}", map1);
    println!("    BTreeMap: {:?}", map2);
    
    // 使用 glob 运算符
    use std::collections::*;
    let mut set = HashSet::new();
    set.insert(1);
    println!("    HashSet: {:?}", set);
    
    println!();
}

// 4. 公有性和私有性
fn privacy_demo() {
    println!("4. 公有性和私有性:");
    
    mod my_module {
        // 私有函数
        fn private_function() {
            println!("    这是私有函数");
        }
        
        // 公有函数
        pub fn public_function() {
            println!("    这是公有函数");
            private_function(); // 模块内部可以调用私有函数
        }
        
        // 公有结构体，但字段可以是私有的
        pub struct PublicStruct {
            pub public_field: String,
            private_field: String,
        }
        
        impl PublicStruct {
            pub fn new(public: &str, private: &str) -> PublicStruct {
                PublicStruct {
                    public_field: String::from(public),
                    private_field: String::from(private),
                }
            }
            
            pub fn get_private(&self) -> &str {
                &self.private_field
            }
        }
        
        // 公有枚举的所有变体都是公有的
        pub enum PublicEnum {
            Variant1,
            Variant2,
        }
    }
    
    // 调用公有函数
    my_module::public_function();
    
    // 使用公有结构体
    let s = my_module::PublicStruct::new("public", "private");
    println!("    公有字段: {}", s.public_field);
    println!("    私有字段（通过方法）: {}", s.get_private());
    
    // 使用公有枚举
    let variant = my_module::PublicEnum::Variant1;
    match variant {
        my_module::PublicEnum::Variant1 => println!("    变体1"),
        my_module::PublicEnum::Variant2 => println!("    变体2"),
    }
    
    println!();
}

// 5. 文件模块演示
fn file_modules_demo() {
    println!("5. 文件模块:");
    
    // 在实际项目中，模块通常定义在单独的文件中
    // 例如：
    // src/
    //   main.rs
    //   lib.rs
    //   front_of_house.rs
    //   front_of_house/
    //     hosting.rs
    //     serving.rs
    
    println!("  文件模块结构示例:");
    println!("    src/");
    println!("      main.rs");
    println!("      lib.rs");
    println!("      front_of_house.rs");
    println!("      front_of_house/");
    println!("        hosting.rs");
    println!("        serving.rs");
    
    // 在 main.rs 中使用模块：
    // mod front_of_house;
    // use front_of_house::hosting;
    
    // 在 front_of_house.rs 中：
    // pub mod hosting;
    // pub mod serving;
    
    // 在 front_of_house/hosting.rs 中：
    // pub fn add_to_waitlist() {
    //     println!("添加到等待列表");
    // }
    
    println!();
}

// 模块系统的高级特性
#[allow(dead_code)]
mod advanced_module_features {
    // 条件编译
    #[cfg(target_os = "windows")]
    fn windows_only() {
        println!("这只在 Windows 上编译");
    }
    
    #[cfg(target_os = "linux")]
    fn linux_only() {
        println!("这只在 Linux 上编译");
    }
    
    // 测试模块
    #[cfg(test)]
    mod tests {
        use super::*;
        
        #[test]
        fn test_something() {
            // 测试代码
        }
    }
    
    // 内联模块
    mod inline_module {
        pub fn inline_function() {
            println!("内联模块函数");
        }
    }
    
    // 使用外部 crate
    // use serde::{Serialize, Deserialize};
    
    // 重新导出
    pub use inline_module::inline_function;
}

// 包和 crate 的概念
#[allow(dead_code)]
fn package_and_crate_concepts() {
    println!("包和 crate 的概念:");
    
    // Package（包）：
    // - 一个 Cargo.toml 文件
    // - 包含一个或多个 crate
    // - 最多包含一个库 crate
    // - 可以包含任意数量的二进制 crate
    
    // Crate（单元包）：
    // - 编译的最小单位
    // - 库 crate 或二进制 crate
    // - crate root 是编译器开始的源文件
    
    // Module（模块）：
    // - 组织代码的方式
    // - 控制私有性
    // - 可以嵌套
    
    println!("  Package 包含多个 crate");
    println!("  Crate 包含模块树");
    println!("  Module 组织和控制作用域");
}

/*
关键要点总结：

1. 模块系统层次：
   - Package（包）> Crate（单元包）> Module（模块）

2. 模块定义：
   - 使用 mod 关键字
   - 可以内联定义或在单独文件中
   - 支持嵌套

3. 路径：
   - 绝对路径：从 crate root 开始
   - 相对路径：从当前模块开始
   - 使用 :: 分隔

4. 公有性：
   - 默认私有
   - 使用 pub 关键字公开
   - 结构体字段可以单独控制
   - 枚举变体跟随枚举的公有性

5. use 关键字：
   - 引入路径到作用域
   - 支持重命名（as）
   - 支持重新导出（pub use）
   - 支持嵌套路径和 glob

6. 文件组织：
   - 模块可以在单独文件中
   - 文件名对应模块名
   - 目录可以包含子模块

编译和运行：
cargo run --bin 10_modules_and_packages
*/
