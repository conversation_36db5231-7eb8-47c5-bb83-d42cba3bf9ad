// 03_functions.rs
// Rust 函数详解

fn main() {
    println!("=== Rust 函数 ===\n");

    // 1. 基本函数
    basic_functions();
    
    // 2. 函数参数
    function_parameters();
    
    // 3. 返回值
    return_values();
    
    // 4. 表达式和语句
    expressions_and_statements();
    
    // 5. 高阶函数和闭包
    higher_order_functions();
}

// 1. 基本函数
fn basic_functions() {
    println!("1. 基本函数:");
    
    // 调用无参数函数
    greet();
    
    // 调用有参数函数
    greet_person("Alice");
    
    println!();
}

// 无参数函数
fn greet() {
    println!("  Hello, World!");
}

// 有参数函数
fn greet_person(name: &str) {
    println!("  Hello, {}!", name);
}

// 2. 函数参数
fn function_parameters() {
    println!("2. 函数参数:");
    
    // 单个参数
    print_number(42);
    
    // 多个参数
    add_numbers(10, 20);
    
    // 不同类型参数
    print_info("Bob", 25, true);
    
    // 可变参数 (通过切片实现)
    print_numbers(&[1, 2, 3, 4, 5]);
    
    println!();
}

fn print_number(x: i32) {
    println!("  数字: {}", x);
}

fn add_numbers(a: i32, b: i32) {
    println!("  {} + {} = {}", a, b, a + b);
}

fn print_info(name: &str, age: u32, is_student: bool) {
    println!("  姓名: {}, 年龄: {}, 学生: {}", name, age, is_student);
}

fn print_numbers(numbers: &[i32]) {
    print!("  数字列表: ");
    for num in numbers {
        print!("{} ", num);
    }
    println!();
}

// 3. 返回值
fn return_values() {
    println!("3. 返回值:");
    
    // 有返回值的函数
    let result = square(5);
    println!("  5 的平方: {}", result);
    
    // 多个返回值 (使用元组)
    let (sum, product) = calculate(4, 6);
    println!("  4 和 6 的和: {}, 积: {}", sum, product);
    
    // 早期返回
    let max = find_max(10, 20);
    println!("  最大值: {}", max);
    
    // 返回 Result 类型
    match divide(10.0, 2.0) {
        Ok(result) => println!("  10 / 2 = {}", result),
        Err(msg) => println!("  错误: {}", msg),
    }
    
    match divide(10.0, 0.0) {
        Ok(result) => println!("  10 / 0 = {}", result),
        Err(msg) => println!("  错误: {}", msg),
    }
    
    println!();
}

// 返回单个值
fn square(x: i32) -> i32 {
    x * x // 表达式，没有分号
}

// 返回多个值
fn calculate(a: i32, b: i32) -> (i32, i32) {
    (a + b, a * b)
}

// 早期返回
fn find_max(a: i32, b: i32) -> i32 {
    if a > b {
        return a; // 早期返回
    }
    b // 最后的表达式
}

// 返回 Result 类型
fn divide(a: f64, b: f64) -> Result<f64, String> {
    if b == 0.0 {
        Err("除数不能为零".to_string())
    } else {
        Ok(a / b)
    }
}

// 4. 表达式和语句
fn expressions_and_statements() {
    println!("4. 表达式和语句:");
    
    // 语句不返回值
    let x = 5; // 这是一个语句
    
    // 表达式返回值
    let y = {
        let x = 3;
        x + 1 // 这是一个表达式，注意没有分号
    };
    
    println!("  x = {}, y = {}", x, y);
    
    // 函数调用是表达式
    let result = add_one(5);
    println!("  add_one(5) = {}", result);
    
    // if 是表达式
    let condition = true;
    let number = if condition { 5 } else { 6 };
    println!("  条件表达式结果: {}", number);
    
    println!();
}

fn add_one(x: i32) -> i32 {
    x + 1
}

// 5. 高阶函数和闭包
fn higher_order_functions() {
    println!("5. 高阶函数和闭包:");
    
    // 闭包基础
    let add = |a, b| a + b;
    println!("  闭包结果: {}", add(2, 3));
    
    // 捕获环境变量
    let multiplier = 3;
    let multiply = |x| x * multiplier;
    println!("  捕获变量: {}", multiply(4));
    
    // 函数作为参数
    let numbers = vec![1, 2, 3, 4, 5];
    let doubled = apply_to_all(numbers.clone(), |x| x * 2);
    println!("  映射结果: {:?}", doubled);
    
    // 使用标准库的高阶函数
    let sum: i32 = numbers.iter().map(|x| x * x).sum();
    println!("  平方和: {}", sum);
    
    let even_numbers: Vec<i32> = numbers.into_iter().filter(|&x| x % 2 == 0).collect();
    println!("  偶数: {:?}", even_numbers);
    
    // 函数指针
    let operation = add_two;
    println!("  函数指针: {}", operation(5));
    
    println!();
}

// 接受闭包作为参数的函数
fn apply_to_all<F>(vec: Vec<i32>, f: F) -> Vec<i32>
where
    F: Fn(i32) -> i32,
{
    vec.into_iter().map(f).collect()
}

// 普通函数，可以作为函数指针
fn add_two(x: i32) -> i32 {
    x + 2
}

// 递归函数示例
#[allow(dead_code)]
fn factorial(n: u64) -> u64 {
    if n <= 1 {
        1
    } else {
        n * factorial(n - 1)
    }
}

// 方法语法 (在 impl 块中定义)
#[allow(dead_code)]
struct Rectangle {
    width: u32,
    height: u32,
}

#[allow(dead_code)]
impl Rectangle {
    // 关联函数 (类似静态方法)
    fn new(width: u32, height: u32) -> Rectangle {
        Rectangle { width, height }
    }
    
    // 方法 (第一个参数是 self)
    fn area(&self) -> u32 {
        self.width * self.height
    }
    
    // 可变方法
    fn double(&mut self) {
        self.width *= 2;
        self.height *= 2;
    }
}

/*
关键要点总结：

1. 函数定义：使用 fn 关键字
2. 参数：必须声明类型
3. 返回值：使用 -> 指定返回类型，最后的表达式作为返回值
4. 语句 vs 表达式：
   - 语句：执行操作但不返回值
   - 表达式：计算并返回值
5. 闭包：匿名函数，可以捕获环境变量
6. 高阶函数：接受函数作为参数或返回函数
7. 方法：在 impl 块中定义，第一个参数通常是 self

编译和运行：
cargo run --bin 03_functions
*/
