// 02_data_types.rs
// Rust 数据类型详解

fn main() {
    println!("=== Rust 数据类型 ===\n");

    // 1. 标量类型
    scalar_types();
    
    // 2. 复合类型
    compound_types();
    
    // 3. 类型推断和注解
    type_inference_and_annotation();
    
    // 4. 类型转换
    type_conversion();
}

// 1. 标量类型 (Scalar Types)
fn scalar_types() {
    println!("1. 标量类型:");
    
    // 整数类型
    integer_types();
    
    // 浮点类型
    floating_point_types();
    
    // 布尔类型
    boolean_type();
    
    // 字符类型
    character_type();
    
    println!();
}

fn integer_types() {
    println!("  整数类型:");
    
    // 有符号整数
    let signed_8: i8 = -128;        // -128 到 127
    let signed_16: i16 = -32_768;   // -32,768 到 32,767
    let signed_32: i32 = -2_147_483_648; // 默认整数类型
    let signed_64: i64 = -9_223_372_036_854_775_808;
    let signed_128: i128 = -170_141_183_460_469_231_731_687_303_715_884_105_728;
    let signed_size: isize = -1000; // 取决于架构 (32位或64位)
    
    println!("    i8: {}", signed_8);
    println!("    i16: {}", signed_16);
    println!("    i32: {}", signed_32);
    println!("    i64: {}", signed_64);
    println!("    i128: {}", signed_128);
    println!("    isize: {}", signed_size);
    
    // 无符号整数
    let unsigned_8: u8 = 255;       // 0 到 255
    let unsigned_16: u16 = 65_535;  // 0 到 65,535
    let unsigned_32: u32 = 4_294_967_295;
    let unsigned_64: u64 = 18_446_744_073_709_551_615;
    let unsigned_128: u128 = 340_282_366_920_938_463_463_374_607_431_768_211_455;
    let unsigned_size: usize = 1000; // 取决于架构
    
    println!("    u8: {}", unsigned_8);
    println!("    u16: {}", unsigned_16);
    println!("    u32: {}", unsigned_32);
    println!("    u64: {}", unsigned_64);
    println!("    u128: {}", unsigned_128);
    println!("    usize: {}", unsigned_size);
    
    // 整数字面量的不同表示法
    let decimal = 98_222;           // 十进制
    let hex = 0xff;                 // 十六进制
    let octal = 0o77;               // 八进制
    let binary = 0b1111_0000;       // 二进制
    let byte = b'A';                // 字节 (仅限 u8)
    
    println!("    十进制: {}", decimal);
    println!("    十六进制: {}", hex);
    println!("    八进制: {}", octal);
    println!("    二进制: {}", binary);
    println!("    字节: {}", byte);
}

fn floating_point_types() {
    println!("  浮点类型:");
    
    let float_32: f32 = 3.14159;    // 单精度
    let float_64: f64 = 2.718281828; // 双精度，默认浮点类型
    
    println!("    f32: {}", float_32);
    println!("    f64: {}", float_64);
    
    // 浮点运算
    let sum = float_32 + 1.0;
    let difference = float_64 - 1.0;
    let product = float_32 * 2.0;
    let quotient = float_64 / 2.0;
    
    println!("    运算结果: {} {} {} {}", sum, difference, product, quotient);
}

fn boolean_type() {
    println!("  布尔类型:");
    
    let is_true: bool = true;
    let is_false: bool = false;
    
    println!("    true: {}", is_true);
    println!("    false: {}", is_false);
    
    // 布尔运算
    let and_result = is_true && is_false;
    let or_result = is_true || is_false;
    let not_result = !is_true;
    
    println!("    AND: {}", and_result);
    println!("    OR: {}", or_result);
    println!("    NOT: {}", not_result);
}

fn character_type() {
    println!("  字符类型:");
    
    let char_a: char = 'A';
    let char_unicode: char = '中';
    let char_emoji: char = '😀';
    let char_escape: char = '\n';
    
    println!("    字符: {}", char_a);
    println!("    Unicode: {}", char_unicode);
    println!("    Emoji: {}", char_emoji);
    println!("    转义字符: {:?}", char_escape);
    
    // char 是 4 字节的 Unicode 标量值
    println!("    char 大小: {} 字节", std::mem::size_of::<char>());
}

// 2. 复合类型 (Compound Types)
fn compound_types() {
    println!("2. 复合类型:");
    
    // 元组类型
    tuple_type();
    
    // 数组类型
    array_type();
    
    println!();
}

fn tuple_type() {
    println!("  元组类型:");
    
    // 创建元组
    let tuple: (i32, f64, u8) = (500, 6.4, 1);
    println!("    元组: {:?}", tuple);
    
    // 解构元组
    let (x, y, z) = tuple;
    println!("    解构: x={}, y={}, z={}", x, y, z);
    
    // 通过索引访问元组元素
    let first = tuple.0;
    let second = tuple.1;
    let third = tuple.2;
    println!("    索引访问: {}, {}, {}", first, second, third);
    
    // 单元类型 (unit type)
    let unit: () = ();
    println!("    单元类型: {:?}", unit);
}

fn array_type() {
    println!("  数组类型:");
    
    // 创建数组
    let array: [i32; 5] = [1, 2, 3, 4, 5];
    println!("    数组: {:?}", array);
    
    // 使用相同值初始化数组
    let same_value = [3; 5]; // [3, 3, 3, 3, 3]
    println!("    相同值数组: {:?}", same_value);
    
    // 访问数组元素
    let first = array[0];
    let second = array[1];
    println!("    访问元素: first={}, second={}", first, second);
    
    // 数组长度
    println!("    数组长度: {}", array.len());
    
    // 数组切片
    let slice = &array[1..4];
    println!("    切片: {:?}", slice);
}

// 3. 类型推断和注解
fn type_inference_and_annotation() {
    println!("3. 类型推断和注解:");
    
    // 类型推断
    let inferred = 42; // Rust 推断为 i32
    println!("  推断类型: {}", inferred);
    
    // 显式类型注解
    let annotated: u64 = 42;
    println!("  注解类型: {}", annotated);
    
    // 在需要时进行类型注解
    let parsed: i32 = "42".parse().expect("不是数字!");
    println!("  解析类型: {}", parsed);
    
    println!();
}

// 4. 类型转换
fn type_conversion() {
    println!("4. 类型转换:");
    
    // 使用 as 进行类型转换
    let integer = 42i32;
    let float = integer as f64;
    let byte = integer as u8;
    
    println!("  as 转换: {} -> {} -> {}", integer, float, byte);
    
    // 使用 From 和 Into trait
    let string = String::from("hello");
    let bytes: Vec<u8> = string.into();
    println!("  Into 转换: 字符串长度 {}", bytes.len());
    
    // 使用 parse 方法
    let number: Result<i32, _> = "123".parse();
    match number {
        Ok(n) => println!("  解析成功: {}", n),
        Err(_) => println!("  解析失败"),
    }
    
    println!();
}

/*
关键要点总结：

1. 标量类型：
   - 整数：i8, i16, i32, i64, i128, isize, u8, u16, u32, u64, u128, usize
   - 浮点：f32, f64
   - 布尔：bool
   - 字符：char (4字节 Unicode)

2. 复合类型：
   - 元组：固定长度，可包含不同类型
   - 数组：固定长度，相同类型

3. 类型推断：Rust 可以自动推断类型
4. 类型转换：使用 as、From/Into trait、parse 方法

编译和运行：
cargo run --bin 02_data_types
*/
